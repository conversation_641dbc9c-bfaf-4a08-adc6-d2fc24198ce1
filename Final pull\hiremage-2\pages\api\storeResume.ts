import type { NextApiRequest, NextApiResponse } from 'next';
import { MongoClient } from 'mongodb';
import { OpenAIEmbeddings, OpenAI } from '@langchain/openai';
import { PDFLoader } from '@langchain/community/document_loaders/fs/pdf';
import { DocxLoader } from '@langchain/community/document_loaders/fs/docx';
import { Document } from 'langchain/document';
import { promises as fs } from 'fs';
import path from 'path';
import { randomUUID } from 'crypto';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import Tesseract from 'tesseract.js';
import { Poppler } from 'node-poppler';
import formidable from 'formidable';
import os from 'os';

interface DocumentDBVector {
  _id: string;
  vectorEmbedding: number[];
  metadata: {
    fileName: string;
    chunkIndex: number;
    fullText: string;
    userId: string;
    uploadDate: string;
    resumeId: string;
  };
}

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const client = new MongoClient(process.env.MONGODB_URI ?? '');
  let filePath: string | undefined;
  
  try {
    console.log('--- Storing resume data in DocumentDB 5.0 with vector search ---');

    await client.connect();
    const db = client.db('chatbot-service-prod');
    const collection = db.collection<DocumentDBVector>('candidate_data');
    console.log('Connected to DocumentDB collection "candidate-data".');

    // Parse form data
    const form = formidable({});
    const [fields, files] = await form.parse(req);
    const file = files.file?.[0];
    const userId = (req.headers['x-user-id'] as string) || 'default-user';

    if (!file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const fileName = file.originalFilename?.toLowerCase() ?? '';
    const allowedExtensions = ['pdf', 'doc', 'docx'];
    const ext = fileName.split('.').pop();
    if (!ext || !allowedExtensions.includes(ext)) {
      console.error('Invalid file type:', fileName);
      return res.status(400).json({ error: 'Invalid file type' });
    }

    // Use the system temporary directory instead of creating one in the project root
    const tempDir = os.tmpdir();
    filePath = path.join(tempDir, `${randomUUID()}-${file.originalFilename ?? ''}`);

    // Copy the uploaded file to the system temp directory
    await fs.copyFile(file.filepath, filePath);
    console.log(`File saved to: ${filePath}`);

    let rawDocs: Document[];
    if (ext === 'pdf') {
      console.log('Using PDFLoader...');
      const loader = new PDFLoader(filePath);
      rawDocs = await loader.load();
    } else {
      console.log('Using DocxLoader...');
      const loader = new DocxLoader(filePath);
      rawDocs = await loader.load();
    }

    let loaderText = rawDocs.map(d => d.pageContent).join('\n');
    console.log(`Loader extracted text length: ${loaderText.length}`);

    let ocrText = '';
    let finalText = loaderText;
    if (ext === 'pdf' && loaderText.length < 300) {
      console.log('Text <300 chars, trying OCR fallback...');
      const poppler = new Poppler();
      const imagesDir = path.join(tempDir, `pdf-images-${randomUUID()}`);
      await fs.mkdir(imagesDir, { recursive: true });

      const outPrefix = path.join(imagesDir, path.parse(filePath).name);
      const pdfCairoOptions = {
        firstPageToConvert: 1,
        jpegFile: true,
        singleFile: false,
      };

      console.log('Converting PDF to images...');
      await poppler.pdfToCairo(filePath, outPrefix, pdfCairoOptions);
      const imageFilenames = await fs.readdir(imagesDir);
      console.log('Image files for OCR:', imageFilenames);

      for (const imgFile of imageFilenames) {
        if (!imgFile.toLowerCase().endsWith('.jpg')) continue;
        const fullImgPath = path.join(imagesDir, imgFile);
        console.log(`Running Tesseract on: ${fullImgPath}`);
        const ocrResult = await Tesseract.recognize(fullImgPath, 'eng');
        console.log(`Tesseract text length: ${ocrResult.data.text.length}`);
        ocrText += '\n' + ocrResult.data.text;
      }

      if (ocrText.trim().length > 50) {
        finalText += '\n' + ocrText;
        console.log(`Appended OCR text. finalText length now: ${finalText.length}`);
      } else {
        console.log('OCR did not yield more text or the PDF is not scanned.');
      }
    }

    if (!finalText.trim()) {
      console.error('No text found. Bailing out.');
      await fs.unlink(filePath);
      return res.status(400).json({ error: 'Could not extract text' });
    }

    const debugTxt = path.join(tempDir, `debug-${randomUUID()}.txt`);
    await fs.writeFile(debugTxt, finalText, 'utf8');
    console.log('Full text saved at', debugTxt);

    const splitter = new RecursiveCharacterTextSplitter({
      chunkSize: 50000,
      chunkOverlap: 0,
    });
    const splittedDocs = await splitter.createDocuments([finalText]);
    console.log(`Split into ${splittedDocs.length} chunk(s).`);

    const embeddings = new OpenAIEmbeddings({ openAIApiKey: process.env.OPENAI_API_KEY });
    const vectors: DocumentDBVector[] = [];
    
    // Generate a single resumeId for all chunks of this resume
    const resumeId = randomUUID();

    for (let i = 0; i < splittedDocs.length; i++) {
      const docText = splittedDocs[i].pageContent;
      const embeddedVector = await embeddings.embedQuery(docText);
      vectors.push({
        _id: randomUUID(),
        vectorEmbedding: embeddedVector,
        metadata: {
          fileName,
          chunkIndex: i + 1,
          fullText: docText,
          userId,
          uploadDate: new Date().toISOString(),
          resumeId, // Use the same resumeId for all chunks
        },
      });
    }

    console.log(`Inserting ${vectors.length} vector(s) to DocumentDB...`);
    await collection.insertMany(vectors);
    console.log('Insert complete.');

    console.log('Summarizing key details with LLM...');
    const llm = new OpenAI({ openAIApiKey: process.env.OPENAI_API_KEY });
    const summaryPrompt = `
      You have the following candidate resume text:
      ${splittedDocs[0].pageContent}

      Please extract the following details in a structured JSON format:
      {
        "name": "...",
        "yearsOfWorkingEachCompany": [...],
        "education": "...",
        "projects": "...",
        "technicalSkills": "...",
        "personalDetails": "...",
        "highlights": "..."
      }

      If a field isn't found, leave it empty or set it to an empty string or array.
    `;
    const summary = await llm.call(summaryPrompt);

    return res.status(200).json({
      message: 'Resume stored successfully in DocumentDB. LLM summary generated.',
      rawDocCount: rawDocs.length,
      finalTextLength: finalText.length,
      splittedChunks: splittedDocs.length,
      debugFile: debugTxt,
      summary,
    });
  } catch (err: any) {
    console.error('Error storing resume + summarizing:', err);
    return res.status(500).json({ error: err.message });
  } finally {
    await client.close();
    // Clean up temporary files
    try {
      if (filePath) {
        try {
          await fs.unlink(filePath);
        } catch (unlinkErr: any) {
          // Only log the error if it's not a "file not found" error
          if (unlinkErr.code !== 'ENOENT') {
            console.error('Error cleaning up temporary file:', unlinkErr);
          }
        }
      }
    } catch (cleanupErr) {
      console.error('Error in cleanup process:', cleanupErr);
    }
  }
} 