import { BorderStyles } from "@/pages/resume-builder/editor/BorderStyleButton";
import useDimensions from "@/hooks/useDimensions";
import { cn } from "@/lib/utils";
import { ResumeValues } from "@/lib/validation";
import { format } from "date-fns";
import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";
import { Badge } from "./ui/badge";

interface ResumePreviewProps {
  resumeData: ResumeValues;
  contentRef?: React.Ref<HTMLDivElement>;
  className?: string;
}

export default function ResumePreview({
  resumeData,
  contentRef,
  className,
}: ResumePreviewProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  const { width } = useDimensions(containerRef);

  return (
    <div
      className={cn(
        "aspect-[210/297] h-fit w-full bg-white text-black shadow-lg rounded-md border border-gray-200",
        className,
      )}
      ref={containerRef}
    >
      <div
        className={cn("space-y-4 pt-3 pb-6 px-6", !width && "opacity-0")}
        style={{
          zoom: width ? (1 / 794) * width : 0.5,
          transform: "scale(1)",
          transformOrigin: "top left",
          minHeight: "100%",
          fontFamily: resumeData.fontFamily || "Arial, sans-serif",
        }}
        ref={contentRef}
        id="resumePreviewContent"
      >
        <PersonalInfoHeader resumeData={resumeData} />
        <SummarySection resumeData={resumeData} />
        <WorkExperienceSection resumeData={resumeData} />
        <EducationSection resumeData={resumeData} />
        <SkillsSection resumeData={resumeData} />
      </div>
    </div>
  );
}

interface ResumeSectionProps {
  resumeData: ResumeValues;
}

function PersonalInfoHeader({ resumeData }: ResumeSectionProps) {
  const {
    photo,
    photoUrl,
    firstName,
    lastName,
    jobTitle,
    city,
    country,
    phone,
    email,
    colorHex,
    borderStyle,
  } = resumeData;

  const [photoSrc, setPhotoSrc] = useState<string | null>(
    photoUrl || (typeof photo === "string" ? photo : "") || null
  );

  useEffect(() => {
    // Log what type of photo we're dealing with for debugging
    console.log("Photo update in preview:", { 
      photoType: photo instanceof File ? 'File' : typeof photo,
      photoUrl 
    });
    
    // Handle File object with highest priority
    if (photo instanceof File) {
      const objectUrl = URL.createObjectURL(photo);
      console.log("Created object URL:", objectUrl);
      setPhotoSrc(objectUrl);
      
      // Clean up the object URL when component unmounts or photo changes
      return () => {
        console.log("Revoking URL:", objectUrl);
        URL.revokeObjectURL(objectUrl);
      };
    } 
    // Then check for photoUrl (from S3)
    else if (photoUrl) {
      setPhotoSrc(photoUrl);
    }
    // Handle string URLs
    else if (typeof photo === "string" && photo) {
      setPhotoSrc(photo);
    } 
    // Handle null/removed photo
    else if (photo === null) {
      setPhotoSrc(null);
    }
  }, [photo, photoUrl]);

  return (
    <div className="flex items-center gap-4">
      {photoSrc && (
        <Image
          src={photoSrc}
          width={90}
          height={90}
          alt="Author photo"
          className="aspect-square object-cover border-2 border-gray-200 shadow-sm"
          style={{
            borderRadius:
              borderStyle === BorderStyles.SQUARE
                ? "0px"
                : borderStyle === BorderStyles.CIRCLE
                  ? "9999px"
                  : "10%",
          }}
        />
      )}
      <div className="space-y-1">
        <div className="space-y-0.5">
          <p
            className="text-2xl font-bold"
            style={{
              color: colorHex,
            }}
          >
            {firstName} {lastName}
          </p>
          <p
            className="font-medium"
            style={{
              color: colorHex,
            }}
          >
            {jobTitle}
          </p>
        </div>
        <p className="text-xs text-gray-500">
          {city}
          {city && country ? ", " : ""}
          {country}
          {(city || country) && (phone || email) ? " • " : ""}
          {[phone, email].filter(Boolean).join(" • ")}
        </p>
      </div>
    </div>
  );
}

function SummarySection({ resumeData }: ResumeSectionProps) {
  const { summary, colorHex } = resumeData;

  if (!summary) return null;

  return (
    <>
      <hr className="border-gray-200" />
      <div className="break-inside-avoid space-y-3">
        <p
          className="text-lg font-semibold"
          style={{
            color: colorHex,
          }}
        >
          Professional profile
        </p>
        <div className="whitespace-pre-line text-sm">{summary}</div>
      </div>
    </>
  );
}

function WorkExperienceSection({ resumeData }: ResumeSectionProps) {
  const { workExperiences, colorHex } = resumeData;

  const workExperiencesNotEmpty = workExperiences?.filter(
    (exp) => Object.values(exp).filter(Boolean).length > 0,
  );

  if (!workExperiencesNotEmpty?.length) return null;

  return (
    <>
      <hr className="border-gray-200" />
      <div className="space-y-3">
        <p
          className="text-lg font-semibold"
          style={{
            color: colorHex,
          }}
        >
          Work experience
        </p>
        {workExperiencesNotEmpty.map((exp, index) => (
          <div key={index} className="break-inside-avoid space-y-1">
            <div
              className="flex items-center justify-between text-sm font-semibold"
              style={{
                color: colorHex,
              }}
            >
              <span>{exp.position}</span>
              {exp.startDate && (
                <span>
                  {format(new Date(exp.startDate), "MM/yyyy")} -{" "}
                  {exp.endDate ? format(new Date(exp.endDate), "MM/yyyy") : "Present"}
                </span>
              )}
            </div>
            <p className="text-xs font-semibold">{exp.company}</p>
            <div className="whitespace-pre-line text-xs">{exp.description}</div>
          </div>
        ))}
      </div>
    </>
  );
}

function EducationSection({ resumeData }: ResumeSectionProps) {
  const { educations, colorHex } = resumeData;

  const educationsNotEmpty = educations?.filter(
    (edu) => Object.values(edu).filter(Boolean).length > 0,
  );

  if (!educationsNotEmpty?.length) return null;

  return (
    <>
      <hr className="border-gray-200" />
      <div className="space-y-3">
        <p
          className="text-lg font-semibold"
          style={{
            color: colorHex,
          }}
        >
          Education
        </p>
        {educationsNotEmpty.map((edu, index) => (
          <div key={index} className="break-inside-avoid space-y-1">
            <div
              className="flex items-center justify-between text-sm font-semibold"
              style={{
                color: colorHex,
              }}
            >
              <span>{edu.degree}</span>
              {edu.startDate && (
                <span>
                  {edu.startDate &&
                    `${format(new Date(edu.startDate), "MM/yyyy")} ${edu.endDate ? `- ${format(new Date(edu.endDate), "MM/yyyy")}` : ""}`}
                </span>
              )}
            </div>
            <p className="text-xs font-semibold">{edu.school}</p>
          </div>
        ))}
      </div>
    </>
  );
}

function SkillsSection({ resumeData }: ResumeSectionProps) {
  return (
    <div className="skills-section">
      <h2 className="text-lg font-semibold mb-2">Skills</h2>
      <div className="skills-grid">
        {resumeData.skills?.map((skill, index) => (
          <div key={index} className="skill-item">
            <span className="skill-bullet">•</span>
            <span className="skill-text">{skill}</span>
          </div>
        ))}
      </div>
      <style jsx>{`
        .skills-section {
          margin-top: 1rem;
        }
        .skills-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 0.5rem;
        }
        .skill-item {
          display: flex;
          align-items: center;
        }
        .skill-bullet {
          margin-right: 0.5rem;
        }
        .skill-text {
          text-align: left;
        }
      `}</style>
    </div>
  );
}
