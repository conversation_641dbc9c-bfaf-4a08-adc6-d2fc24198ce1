import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { FileUserIcon, PenLineIcon, DownloadIcon, CheckCircle, SaveIcon } from "lucide-react";
import Link from "next/link";
import { steps } from "@/lib/resume-builder/editor/steps";

interface FooterProps {
  currentStep: string;
  setCurrentStep: (step: string) => void;
  showSmResumePreview: boolean;
  setShowSmResumePreview: (show: boolean) => void;
  isSaving: boolean;
  handleSave: () => Promise<void>;
  hasUnsavedChanges: boolean;
}

export default function Footer({
  currentStep,
  setCurrentStep,
  showSmResumePreview,
  setShowSmResumePreview,
  isSaving,
  handleSave,
  hasUnsavedChanges,
}: FooterProps) {
  const previousStep = steps.find(
    (_, index) => steps[index + 1]?.key === currentStep,
  )?.key;

  const nextStep = steps.find(
    (_, index) => steps[index - 1]?.key === currentStep,
  )?.key;

  const isLastStep = !nextStep;

  const handleDownload = () => {
    const resumeContent = document.getElementById("resumePreviewContent");
    if (!resumeContent) return;

    // Create a new window for the printable content
    const printWindow = window.open("", "_blank");
    if (!printWindow) return;

    // Clone the content to avoid modifying the original
    const contentClone = resumeContent.cloneNode(true) as HTMLElement;
    
    // Get the selected font family from the resumeData's inline style
    const computedStyle = window.getComputedStyle(resumeContent);
    const fontFamily = computedStyle.fontFamily || resumeContent.style.fontFamily || "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif";
    
    // Function to convert blob URLs to data URLs
    const convertBlobToDataURL = async (blobUrl: string): Promise<string> => {
      return new Promise((resolve, reject) => {
        try {
          // Fetch the blob
          fetch(blobUrl)
            .then(response => response.blob())
            .then(blob => {
              // Create a FileReader to read the blob as a data URL
              const reader = new FileReader();
              reader.onloadend = () => {
                resolve(reader.result as string);
              };
              reader.onerror = reject;
              reader.readAsDataURL(blob);
            })
            .catch(reject);
        } catch (error) {
          reject(error);
        }
      });
    };

    // Process images to convert blob URLs to data URLs
    const processImages = async () => {
    const images = contentClone.querySelectorAll('img');
      const conversionPromises: Promise<void>[] = [];
      
      // Convert NodeList to Array to fix TypeScript error
      Array.from(images).forEach(img => {
      if (img.src && img.src.startsWith('blob:')) {
          const conversionPromise = convertBlobToDataURL(img.src)
            .then(dataUrl => {
              img.src = dataUrl;
            })
            .catch(error => {
              console.error('Failed to convert blob URL to data URL:', error);
            });
          
          conversionPromises.push(conversionPromise);
        }
      });
      
      // Wait for all image conversions to complete
      return Promise.all(conversionPromises);
    };

    // Process all images and then render the content
    processImages().then(() => {
      // Map common resume fonts to Google Fonts imports
      const fontImports: Record<string, string> = {
        'Garamond': '', // Garamond is system font
        'Gill Sans': '', // Gill Sans is system font
        'Cambria': '', // Cambria is system font
        'Calibri': '', // Calibri is system font
        'Constantia': '', // Constantia is system font
        'Lato': '@import url("https://fonts.googleapis.com/css2?family=Lato:wght@400;500;600;700&display=swap");',
        'Didot': '', // Didot is system font
        'Helvetica': '', // Helvetica is system font
        'Georgia': '', // Georgia is system font
        'Avenir': '', // Avenir is system font
      };
      
      // Extract font name from the fontFamily string
      const fontName = fontFamily.split(',')[0].replace(/['"]/g, '').trim();
      const googleFontImport = fontImports[fontName] || '';
      
      // Render the content in the new window
      printWindow.document.write(`
        <html>
          <head>
            <title>Resume</title>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
                ${googleFontImport}
                
                /* Base styles */
                * {
                  box-sizing: border-box;
                  margin: 0;
                  padding: 0;
                }
                
              body {
                margin: 0;
                padding: 0;
                display: flex;
                justify-content: center;
                  align-items: flex-start;
                min-height: 100vh;
                background-color: #f5f5f5;
                  font-family: ${fontFamily};
                  line-height: 1.5;
              }
                
              .resume-container {
                background-color: white;
                padding: 2rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                width: 8.5in;
                min-height: 11in;
                  margin: 2rem auto;
                  font-family: ${fontFamily};
                }
                
                /* Layout components */
                .flex {
                  display: flex !important;
                }
                
                .items-center {
                  align-items: center !important;
                }
                
                .gap-4 {
                  gap: 1rem !important;
                }
                
                .flex-wrap {
                  flex-wrap: wrap !important;
                }
                
                .gap-2 {
                  gap: 0.5rem !important;
                }
                
                /* Flex containers for job titles and dates */
                .flex span:first-child {
                  margin-right: auto !important;
                  padding-right: 1rem !important;
                }
                
                .flex span:last-child {
                  text-align: right !important;
                  white-space: nowrap !important;
                }
                
                .space-y-1 > * + * {
                  margin-top: 0.25rem !important;
                }
                
                .space-y-3 > * + * {
                  margin-top: 0.75rem !important;
                }
                
                .space-y-4 > * + * {
                  margin-top: 1rem !important;
                }
                
                /* Personal info section styling */
                .resume-container > div:first-child > .flex {
                  display: flex !important;
                  align-items: center !important;
                  gap: 1rem !important;
                }
                
                .resume-container > div:first-child > .flex > div {
                  display: flex !important;
                  flex-direction: column !important;
                }
                
                /* Image styling */
                img {
                  width: 90px !important;
                  height: 90px !important;
                  object-fit: cover !important;
                  border: 2px solid #e5e7eb !important;
                  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
                }
                
                /* Typography */
                .text-2xl {
                  font-size: 1.5rem !important;
                  line-height: 2rem !important;
                  font-weight: 700 !important;
                  margin-bottom: 0.25rem !important;
                }
                
                .text-lg {
                  font-size: 1.125rem !important;
                  line-height: 1.75rem !important;
                  font-weight: 600 !important;
                  margin-bottom: 0.5rem !important;
                }
                
                .font-medium {
                  font-weight: 500 !important;
                }
                
                .text-xs {
                  font-size: 0.75rem !important;
                  line-height: 1rem !important;
                  color: #6b7280 !important;
                }
                
                .font-semibold {
                  font-weight: 600 !important;
                }
                
                .text-sm {
                  font-size: 0.875rem !important;
                  line-height: 1.25rem !important;
                }
                
                .whitespace-pre-line {
                  white-space: pre-line !important;
                }
                
                /* Horizontal dividers */
                hr {
                  border: none !important;
                  border-top: 1px solid #e5e7eb !important;
                  margin: 1rem 0 !important;
                }
                
                /* Skills badges - target with multiple selectors to ensure they're caught */
                [class*="badge"],
                .badge,
                span[class*="badge"],
                div[class*="badge"],
                p[class*="badge"] {
                  display: none !important;
                }
                
                /* Don't hide the new skill format */
                .skills-item,
                .skills-item span,
                div.skills-item {
                  display: flex !important;
                }
                
                /* Skills container styling for print */
                .skills-container {
                  display: grid !important;
                  grid-template-columns: repeat(3, 1fr) !important;
                  gap: 0 !important;
                  column-gap: 8px !important;
                  row-gap: 0 !important;
                  width: 100% !important;
                  font-family: inherit !important;
                }
                
                /* Skills item styling for print */
                .skills-item {
                  display: flex !important;
                  align-items: flex-start !important;
                  margin-bottom: 0 !important;
                  padding-bottom: 0 !important;
                  width: 100% !important;
                  break-inside: avoid !important;
                  font-family: inherit !important;
                  font-size: 0.7rem !important;
                }
                
                .skills-item span {
                  font-family: inherit !important;
                  font-size: 0.7rem !important;
                  line-height: 1rem !important;
                  font-weight: normal !important;
                }
                
                .skills-bullet {
                  margin-right: 0.25rem !important;
                }
                
                /* Section styles */
                .section {
                  margin-bottom: 1.5rem !important;
                }
                
                .break-inside-avoid {
                  break-inside: avoid !important;
                }
                
                /* Print-specific styles */
                @media print {
                  @page {
                    size: letter portrait;
                    margin: 0;
                  }
                  
                  body {
                    background: none !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                  }
                  
                  .resume-container {
                    width: 100% !important;
                    height: 100% !important;
                    margin: 0 !important;
                    padding: 0.5in !important;
                    box-shadow: none !important;
                    print-color-adjust: exact !important;
                  }
                  
                  /* Ensure badges print with colors */
                  [class*="badge"],
                  .badge,
                  span[class*="badge"],
                  div[class*="badge"],
                  p[class*="badge"] {
                    background-color: transparent !important;
                    color: black !important;
                    print-color-adjust: exact !important;
                    -webkit-print-color-adjust: exact !important;
                    forced-color-adjust: none !important;
                    position: relative !important;
                    padding-left: 1rem !important;
                  }
                  
                  /* Ensure bullet points print */
                  [class*="badge"]::before,
                  .badge::before,
                  span[class*="badge"]::before,
                  div[class*="badge"]::before,
                  p[class*="badge"]::before {
                    content: "•" !important;
                    position: absolute !important;
                    left: 0 !important;
                    top: 0 !important;
                  }
                }
                
                /* Skills styling for the downloaded resume */
                .skills-grid {
                  display: grid !important;
                  grid-template-columns: repeat(3, 1fr) !important;
                  gap: 0.25rem 1rem !important;
                  margin-top: 0.5rem !important;
                  width: 100% !important;
                }
                
                .skill-item {
                  display: flex !important;
                  align-items: flex-start !important;
                  color: black !important;
                  font-size: 0.75rem !important;
                  line-height: 1.25rem !important;
                }
                
                .skill-bullet {
                  margin-right: 0.25rem !important;
                }

                /* Work experience and education specific styling */
                .work-section .flex,
                .education-section .flex {
                  display: flex !important;
                  justify-content: space-between !important;
                  align-items: flex-start !important;
                  width: 100% !important;
                }
                
                /* Title styling for work and education */
                .work-section .flex span:first-child,
                .education-section .flex span:first-child {
                  font-weight: 600 !important;
                  margin-right: auto !important;
                  padding-right: 2rem !important;
                  text-align: left !important;
                  min-width: 50% !important;
                }
                
                /* Date styling for work and education */
                .work-section .flex span:last-child,
                .education-section .flex span:last-child {
                  text-align: right !important;
                  white-space: nowrap !important;
                  flex-shrink: 0 !important;
                }
                
                /* Completely separate skills section styling */
                .skills-section .skills-grid,
                .skills-section .grid {
                  display: grid !important;
                  grid-template-columns: repeat(3, 1fr) !important;
                  gap: 0.25rem 1rem !important;
                  width: 100% !important;
                }
                
                .skills-section .skill-item {
                  display: flex !important;
                  flex-direction: row !important;
                  justify-content: flex-start !important;
                  align-items: flex-start !important;
                  margin: 0 !important;
                  padding: 0 !important;
                  min-width: auto !important;
                }
                
                .skills-section span {
                  display: inline !important;
                  text-align: left !important;
                  white-space: normal !important;
                  flex: none !important;
                  margin-right: 0.25rem !important;
                  padding-right: 0 !important;
                  min-width: auto !important;
                  font-weight: normal !important;
                }
                
                /* Override any global span styles that might conflict */
                .skills-section span:first-child,
                .skills-section span:last-child {
                  text-align: left !important;
                  white-space: normal !important;
                  flex-shrink: 1 !important;
                  min-width: auto !important;
                  margin-right: 0.25rem !important;
                  padding-right: 0 !important;
                }
              </style>
            </head>
            <body>
              <div class="resume-container">
                ${contentClone.innerHTML}
              </div>
                <script>
                  // Apply additional dynamic styling to ensure layout is preserved
                  document.addEventListener('DOMContentLoaded', function() {
                    // Process header/personal info section
                    const personalInfoSection = document.querySelector('.resume-container > div:first-child');
                    if (personalInfoSection) {
                      const headerFlex = personalInfoSection.querySelector('.flex');
                      if (headerFlex) {
                        headerFlex.style.display = 'flex';
                        headerFlex.style.alignItems = 'center';
                        headerFlex.style.gap = '1rem';
                      }
                    }
                    
                    // Find and mark all sections with appropriate classes
                    const allSectionHeaders = Array.from(document.querySelectorAll('.resume-container p, .resume-container h1, .resume-container h2, .resume-container h3, .resume-container h4, .resume-container h5, .resume-container h6'))
                      .filter(el => el.textContent && ['Work experience', 'Education', 'Skills'].includes(el.textContent.trim()));
                    
                    // Process each identified section header
                    allSectionHeaders.forEach(header => {
                      const sectionType = header.textContent.trim();
                      const sectionContainer = header.closest('div');
                      
                      if (sectionContainer) {
                        // Add specific class based on section type
                        if (sectionType === 'Work experience') {
                          sectionContainer.classList.add('work-section');
                        } else if (sectionType === 'Education') {
                          sectionContainer.classList.add('education-section');
                        } else if (sectionType === 'Skills') {
                          sectionContainer.classList.add('skills-section');
                        }
                      }
                    });
                    
                    // Process each section type separately
                    
                    // 1. Skills Section processed below with existing code
                    const skillsSection = document.querySelector('.skills-section');
                    
                    // 2. Process Work and Education Sections
                    const workSection = document.querySelector('.work-section');
                    const educationSection = document.querySelector('.education-section');
                    
                    // Function to process date containers in work and education sections
                    const processSectionDateContainers = (section) => {
                      if (!section) return;
                      
                      const flexContainers = section.querySelectorAll('.flex');
                      flexContainers.forEach(container => {
                        if (container.querySelectorAll('span').length >= 2) {
                          // This is a job title + date container
                          container.style.display = 'flex';
                          container.style.justifyContent = 'space-between';
                          container.style.width = '100%';
                          
                          // Style the position/title span (first span)
                          const titleSpan = container.querySelector('span:first-child');
                          if (titleSpan) {
                            titleSpan.style.fontWeight = '600';
                            titleSpan.style.marginRight = 'auto';
                            titleSpan.style.paddingRight = '2rem';
                            titleSpan.style.textAlign = 'left';
                            titleSpan.style.minWidth = '50%';
                          }
                          
                          // Style the date span (second span)
                          const dateSpan = container.querySelector('span:last-child');
                          if (dateSpan) {
                            dateSpan.style.textAlign = 'right';
                            dateSpan.style.whiteSpace = 'nowrap';
                            dateSpan.style.flexShrink = '0';
                            
                            // Fix date spacing
                            const dateText = dateSpan.textContent || '';
                            if (dateText.includes('-')) {
                              const fixedText = dateText
                                .replace(/(\d{2}\/\d{4})-(\d{2}\/\d{4})/g, '$1 - $2')  // MM/YYYY-MM/YYYY
                                .replace(/(\d{2}\/\d{4}) *- *(\d{2}\/\d{4})/g, '$1 - $2')  // Normalize existing spaces
                                .replace(/(\d{2}\/\d{2}\/\d{4})-(\d{2}\/\d{2}\/\d{4})/g, '$1 - $2')  // DD/MM/YYYY-DD/MM/YYYY
                                .replace(/(\d{2}\/\d{4})-([A-Za-z]+)/g, '$1 - $2')  // MM/YYYY-Present
                                .replace(/(\d{4})-(\d{4})/g, '$1 - $2')  // YYYY-YYYY
                                .replace(/(\d{2})-(\d{2})/g, '$1 - $2')  // MM-MM
                                .replace(/(\d{2})\/(\d{4})-(\d{2})\/(\d{4})/g, '$1/$2 - $3/$4')  // Alternative MM/YYYY-MM/YYYY format
                                .replace(/(\d{2})\/(\d{4})-([A-Za-z]+)/g, '$1/$2 - $3')  // Alternative MM/YYYY-Present format
                                .replace(/(\d{2})\/(\d{2})\/(\d{4})-(\d{2})\/(\d{2})\/(\d{4})/g, '$1/$2/$3 - $4/$5/$6'); // DD/MM/YYYY-DD/MM/YYYY
                              
                              dateSpan.textContent = fixedText;
                            }
                          }
                        }
                      });
                    };
                    
                    // Process both work and education sections
                    processSectionDateContainers(workSection);
                    processSectionDateContainers(educationSection);
                    
                    // Now continue with existing skills section processing
                    if (skillsSection) {
                      // Find all skill items
                      let skillsText = [];
                      
                      // Try to find skills from existing grid layout
                      const existingGrid = skillsSection.querySelector('.grid');
                      if (existingGrid) {
                        // Already has grid layout, just ensure proper styling
                        existingGrid.classList.add('skills-grid');
                        existingGrid.classList.remove('flex'); // Remove flex class if present
                        existingGrid.style.display = 'grid';
                        existingGrid.style.gridTemplateColumns = 'repeat(3, 1fr)';
                        existingGrid.style.gap = '0.25rem 1rem';
                        
                        // Style each skill item
                        const skillItems = existingGrid.querySelectorAll('div');
                        skillItems.forEach(item => {
                          item.classList.add('skill-item');
                          item.style.color = 'black';
                          item.style.fontSize = '0.75rem';
                          
                          // Style the bullet point
                          const bullet = item.querySelector('span:first-child');
                          if (bullet) {
                            bullet.classList.add('skill-bullet');
                            bullet.style.marginRight = '0.25rem';
                          }
                          
                          // Style the text
                          const text = item.querySelector('span:last-child');
                          if (text) {
                            text.classList.add('skill-text');
                            text.style.color = 'black';
                          }
                        });
                        
                        return;
                      }
                      
                      // Try to find skills from various sources
                      // Check for list items first
                      const existingList = skillsSection.querySelector('ul');
                      if (existingList) {
                        const listItems = existingList.querySelectorAll('li');
                        skillsText = Array.from(listItems).map(li => li.textContent?.trim()).filter(Boolean);
                      } else {
                        // Try to find skills from divs or spans
                        const container = skillsSection.querySelector('div');
                        if (container) {
                          skillsText = Array.from(container.querySelectorAll('div, span'))
                            .map(el => el.textContent?.replace(/•/g, '').trim())
                            .filter(text => text && text.length > 0);
                        }
                      }
                      
                      // Create grid container
                      const skillsGrid = document.createElement('div');
                      skillsGrid.className = 'skills-grid';
                      skillsGrid.style.display = 'grid';
                      skillsGrid.style.gridTemplateColumns = 'repeat(3, 1fr)';
                      skillsGrid.style.gap = '0.25rem 1rem';
                      skillsGrid.style.marginTop = '0.5rem';
                      skillsGrid.style.width = '100%';
                      
                      // Add skills to grid
                      skillsText.forEach(skill => {
                        if (!skill) return;
                        
                        const skillItem = document.createElement('div');
                        skillItem.className = 'skill-item';
                        skillItem.style.display = 'flex';
                        skillItem.style.flexDirection = 'row';
                        skillItem.style.justifyContent = 'flex-start';
                        skillItem.style.alignItems = 'flex-start';
                        skillItem.style.color = 'black';
                        skillItem.style.fontSize = '0.75rem';
                        skillItem.style.margin = '0';
                        skillItem.style.padding = '0';
                        skillItem.style.minWidth = 'auto';
                        skillItem.style.fontFamily = 'inherit';
                        
                        const bullet = document.createElement('span');
                        bullet.className = 'skill-bullet';
                        bullet.textContent = '•';
                        bullet.style.marginRight = '0.25rem';
                        bullet.style.display = 'inline';
                        bullet.style.textAlign = 'left';
                        bullet.style.whiteSpace = 'normal';
                        bullet.style.fontFamily = 'inherit';
                        
                        const text = document.createElement('span');
                        text.className = 'skill-text';
                        text.textContent = skill;
                        text.style.color = 'black';
                        text.style.display = 'inline';
                        text.style.textAlign = 'left';
                        text.style.whiteSpace = 'normal';
                        text.style.fontFamily = 'inherit';
                        
                        skillItem.appendChild(bullet);
                        skillItem.appendChild(text);
                        skillsGrid.appendChild(skillItem);
                      });
                      
                      // Replace existing content with grid
                      if (existingList) {
                        existingList.parentNode.replaceChild(skillsGrid, existingList);
                      } else {
                        const container = skillsSection.querySelector('.flex, .grid, div:not(:first-child)');
                        if (container) {
                          container.parentNode.replaceChild(skillsGrid, container);
                        } else {
                          // Add after the header
                          const header = skillsSection.querySelector('p, h1, h2, h3, h4, h5, h6');
                          if (header) {
                            header.parentNode.insertBefore(skillsGrid, header.nextSibling);
                          }
                        }
                      }
                    }
                  });
                  
                  // Run the above styling immediately as well
                  (function() {
                    // Process header/personal info section
                    const personalInfoSection = document.querySelector('.resume-container > div:first-child');
                    if (personalInfoSection) {
                      const headerFlex = personalInfoSection.querySelector('.flex');
                      if (headerFlex) {
                        headerFlex.style.display = 'flex';
                        headerFlex.style.alignItems = 'center';
                        headerFlex.style.gap = '1rem';
                      }
                    }
                    
                    // Find and mark all sections with appropriate classes
                    const allSectionHeaders = Array.from(document.querySelectorAll('.resume-container p, .resume-container h1, .resume-container h2, .resume-container h3, .resume-container h4, .resume-container h5, .resume-container h6'))
                      .filter(el => el.textContent && ['Work experience', 'Education', 'Skills'].includes(el.textContent.trim()));
                    
                    // Process each identified section header
                    allSectionHeaders.forEach(header => {
                      const sectionType = header.textContent.trim();
                      const sectionContainer = header.closest('div');
                      
                      if (sectionContainer) {
                        // Add specific class based on section type
                        if (sectionType === 'Work experience') {
                          sectionContainer.classList.add('work-section');
                        } else if (sectionType === 'Education') {
                          sectionContainer.classList.add('education-section');
                        } else if (sectionType === 'Skills') {
                          sectionContainer.classList.add('skills-section');
                        }
                      }
                    });
                    
                    // Process each section type separately
                    
                    // 1. Process Work and Education Sections
                    const workSection = document.querySelector('.work-section');
                    const educationSection = document.querySelector('.education-section');
                    
                    // Function to process date containers in work and education sections
                    const processSectionDateContainers = (section) => {
                      if (!section) return;
                      
                      const flexContainers = section.querySelectorAll('.flex');
                      flexContainers.forEach(container => {
                        if (container.querySelectorAll('span').length >= 2) {
                          // This is a job title + date container
                          container.style.display = 'flex';
                          container.style.justifyContent = 'space-between';
                          container.style.width = '100%';
                          
                          // Style the position/title span (first span)
                          const titleSpan = container.querySelector('span:first-child');
                          if (titleSpan) {
                            titleSpan.style.fontWeight = '600';
                            titleSpan.style.marginRight = 'auto';
                            titleSpan.style.paddingRight = '2rem';
                            titleSpan.style.textAlign = 'left';
                            titleSpan.style.minWidth = '50%';
                          }
                          
                          // Style the date span (second span)
                          const dateSpan = container.querySelector('span:last-child');
                          if (dateSpan) {
                            dateSpan.style.textAlign = 'right';
                            dateSpan.style.whiteSpace = 'nowrap';
                            dateSpan.style.flexShrink = '0';
                            
                            // Fix date spacing
                            const dateText = dateSpan.textContent || '';
                            if (dateText.includes('-')) {
                              const fixedText = dateText
                                .replace(/(\d{2}\/\d{4})-(\d{2}\/\d{4})/g, '$1 - $2')  // MM/YYYY-MM/YYYY
                                .replace(/(\d{2}\/\d{4}) *- *(\d{2}\/\d{4})/g, '$1 - $2')  // Normalize existing spaces
                                .replace(/(\d{2}\/\d{2}\/\d{4})-(\d{2}\/\d{2}\/\d{4})/g, '$1 - $2')  // DD/MM/YYYY-DD/MM/YYYY
                                .replace(/(\d{2}\/\d{4})-([A-Za-z]+)/g, '$1 - $2')  // MM/YYYY-Present
                                .replace(/(\d{4})-(\d{4})/g, '$1 - $2')  // YYYY-YYYY
                                .replace(/(\d{2})-(\d{2})/g, '$1 - $2')  // MM-MM
                                .replace(/(\d{2})\/(\d{4})-(\d{2})\/(\d{4})/g, '$1/$2 - $3/$4')  // Alternative MM/YYYY-MM/YYYY format
                                .replace(/(\d{2})\/(\d{4})-([A-Za-z]+)/g, '$1/$2 - $3')  // Alternative MM/YYYY-Present format
                                .replace(/(\d{2})\/(\d{2})\/(\d{4})-(\d{2})\/(\d{2})\/(\d{4})/g, '$1/$2/$3 - $4/$5/$6'); // DD/MM/YYYY-DD/MM/YYYY
                              
                              dateSpan.textContent = fixedText;
                            }
                          }
                        }
                      });
                    };
                    
                    // Process both work and education sections
                    processSectionDateContainers(workSection);
                    processSectionDateContainers(educationSection);
                    
                    // 2. Process Skills Section
                    const skillsSection = document.querySelector('.skills-section');
                    if (skillsSection) {
                      // Process skills... (implementation similar to above)
                    }
                  })();
                </script>
            </body>
          </html>
        `);

    printWindow.document.close();
    printWindow.focus();
    
      // Give a slight delay for styles to apply and images to load
    setTimeout(() => {
        try {
      printWindow.print();
        } catch (e) {
          console.error('Error during print operation:', e);
        }
      }, 1000);
    });
  };

  return (
    <footer className="sticky bottom-0 w-full border-t bg-background px-3 py-3 shadow-md z-10">
      <div className="mx-auto flex max-w-7xl flex-wrap justify-between gap-2">
        <div className="flex items-center gap-2">
          <Button
            variant="secondary"
            onClick={
              previousStep ? () => setCurrentStep(previousStep) : undefined
            }
            disabled={!previousStep}
            size="sm"
            className="sm:size-md"
          >
            Previous
          </Button>
          {isLastStep ? (
            <div className="flex items-center gap-2">
              <Button 
                onClick={handleSave} 
                disabled={isSaving || !hasUnsavedChanges}
                size="sm" 
                className="sm:size-md"
              >
                <SaveIcon className="mr-1 h-4 w-4 sm:mr-2" />
                <span className="hidden sm:inline">Save Resume</span>
                <span className="sm:hidden">Save</span>
              </Button>
              <Button onClick={handleDownload} size="sm" className="sm:size-md">
                <DownloadIcon className="mr-1 h-4 w-4 sm:mr-2" />
                <span className="hidden sm:inline">Download Resume</span>
                <span className="sm:hidden">Download</span>
              </Button>
            </div>
          ) : (
            <Button
              onClick={nextStep ? () => setCurrentStep(nextStep) : undefined}
              disabled={!nextStep}
              size="sm"
              className="sm:size-md"
            >
              Next
            </Button>
          )}
        </div>
        <Button
          variant="outline"
          size="icon"
          onClick={() => setShowSmResumePreview(!showSmResumePreview)}
          className="fixed bottom-16 right-4 z-50 h-12 w-12 rounded-full bg-background shadow-lg md:hidden"
          title={
            showSmResumePreview ? "Show input form" : "Show resume preview"
          }
        >
          {showSmResumePreview ? <PenLineIcon /> : <FileUserIcon />}
        </Button>
        <div className="flex items-center gap-2">
          {!isLastStep && (
            <div className={cn(
              "flex items-center px-3 py-1.5 rounded-md font-medium text-sm transition-all duration-300",
              isSaving 
                ? "bg-blue-100 text-blue-700" 
                : hasUnsavedChanges 
                  ? "bg-yellow-100 text-yellow-700"
                  : "bg-green-100 text-green-700 opacity-0"
            )}>
              {isSaving ? (
                <>
                  <SaveIcon className="mr-1.5 h-4 w-4 animate-pulse" />
                  Saving...
                </>
              ) : hasUnsavedChanges ? (
                <>
                  <SaveIcon className="mr-1.5 h-4 w-4" />
                  Unsaved changes
                </>
              ) : (
                <>
                  <CheckCircle className="mr-1.5 h-4 w-4" />
                  Saved
                </>
              )}
            </div>
          )}
          <Button variant="secondary" asChild size="sm" className="sm:size-md">
            <Link href="/resume-builder/resumes">Close</Link>
          </Button>
        </div>
      </div>
    </footer>
  );
}
