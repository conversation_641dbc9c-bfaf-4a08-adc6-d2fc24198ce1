import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { PaletteIcon } from "lucide-react";
import { useState } from "react";
import { Color, ColorChangeHandler, TwitterPicker } from "react-color";

interface ColorPickerProps {
  color: Color | undefined;
  onChange: ColorChangeHandler;
}

export default function ColorPicker({ color, onChange }: ColorPickerProps) {
  const [showPopover, setShowPopover] = useState(false);
  
  // Safely extract color value for styling
  let colorValue = '#000000';
  if (typeof color === 'string') {
    colorValue = color;
  } else if (color && typeof color === 'object') {
    if ('hex' in color && typeof color.hex === 'string') {
      colorValue = color.hex;
    } else if ('r' in color && 'g' in color && 'b' in color) {
      // Handle RGB format
      const { r, g, b } = color;
      colorValue = `rgb(${r}, ${g}, ${b})`;
    }
  }

  return (
    <Popover open={showPopover} onOpenChange={setShowPopover}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="icon"
          title="Change resume color"
          onClick={() => setShowPopover(true)}
          className="relative overflow-hidden border-2"
          style={{ borderColor: colorValue }}
        >
          <div 
            className="absolute inset-0 opacity-70" 
            style={{ backgroundColor: colorValue }}
          />
          <PaletteIcon className="size-5 relative z-10 text-foreground" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="border-none bg-transparent shadow-none"
        align="end"
      >
        <TwitterPicker color={color} onChange={onChange} triangle="top-right" />
      </PopoverContent>
    </Popover>
  );
}
