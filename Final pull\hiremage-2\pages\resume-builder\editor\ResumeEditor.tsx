"use client";

import useUnloadWarning from "@/hooks/useUnloadWarning";
import { cn } from "@/lib/utils";
import { ResumeValues } from "@/lib/validation";
import { useRouter } from "next/router";
import dynamic from "next/dynamic";
import { useEffect, useState, useCallback } from "react";
import Breadcrumbs from "./Breadcrumbs";
import Footer from "./Footer";
import { steps } from "@/lib/resume-builder/editor/steps";
import { Button } from "@/components/ui/button";
import { SaveIcon } from "lucide-react";
import { saveResume } from "@/lib/resume-builder/editor/actions";
import { fileReplacer } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";

// Import components with no SSR to avoid hydration mismatch
const ResumePreviewSection = dynamic(() => import("./ResumePreviewSection"), {
  ssr: false
});

interface ResumeEditorProps {
  resumeToEdit: ResumeValues | null;
}

export default function ResumeEditor({ resumeToEdit }: ResumeEditorProps) {
  const router = useRouter();
  const { step } = router.query;
  const { toast } = useToast();

  const [resumeData, setResumeData] = useState<ResumeValues>(
    resumeToEdit || {},
  );

  const [showSmResumePreview, setShowSmResumePreview] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSavedData, setLastSavedData] = useState<ResumeValues>({} as ResumeValues);
  
  // Calculate if there are unsaved changes
  const hasUnsavedChanges = JSON.stringify(resumeData, fileReplacer) !== 
                           JSON.stringify(lastSavedData, fileReplacer);

  // Set up warning for unsaved changes when navigating away
  useUnloadWarning(hasUnsavedChanges);

  const currentStep = typeof step === 'string' ? step : steps[0].key;

  function setStep(key: string) {
    const query = { ...router.query, step: key };
    router.push({ pathname: router.pathname, query }, undefined, { shallow: true });
  }

  useEffect(() => {
    // If no step is specified in the URL, set it to the first step
    if (!step) {
      setStep(steps[0].key);
    }
    
    // Initialize lastSavedData and resumeData only once when the component first mounts
    // or when resumeToEdit changes (not when steps change)
    if (resumeToEdit && (!resumeData.id || resumeData.id !== resumeToEdit.id)) {
      const safeResumeToEdit = JSON.parse(JSON.stringify(resumeToEdit));
      setLastSavedData(safeResumeToEdit);
      setResumeData(safeResumeToEdit);
    }
  }, [step, resumeToEdit, resumeData.id]);

  // Debug logging for change detection
  useEffect(() => {
    const resumeStr = JSON.stringify(resumeData, fileReplacer);
    const lastSavedStr = JSON.stringify(lastSavedData, fileReplacer);
    const changes = resumeStr !== lastSavedStr;
    console.log("Change detection:", { hasChanges: changes });
    
    // Check if we have a File object for the photo but also a photoUrl
    // If so, clear the photoUrl to ensure the File is used for preview
    if (resumeData.photo instanceof File && resumeData.photoUrl) {
      console.log("Detected File object with photoUrl - clearing photoUrl for preview");
      setResumeData({
        ...resumeData,
        photoUrl: undefined
      });
    }
  }, [resumeData, lastSavedData]);

  const handleSave = useCallback(async () => {
    try {
      setIsSaving(true);
      console.log("🖱️ Manual save triggered");
      
      const result = await saveResume({
        ...resumeData,
        id: resumeData.id,
      });
      
      // Update lastSavedData to reflect the current state
      setLastSavedData(JSON.parse(JSON.stringify(resumeData)));
      
      // Update URL if needed
      if (router.query.resumeId !== result.id) {
        const query = { ...router.query, resumeId: result.id };
        router.replace({ pathname: router.pathname, query }, undefined, { shallow: true });
      }
      
      toast({
        title: "Resume saved",
        description: "Your resume has been saved successfully.",
      });
      
    } catch (error) {
      console.error("❌ Save failed:", error);
      toast({
        variant: "destructive",
        title: "Save failed",
        description: "Could not save your resume. Please try again.",
      });
    } finally {
      setIsSaving(false);
    }
  }, [resumeData, router, toast]);

  const FormComponent = steps.find(
    (step) => step.key === currentStep,
  )?.component;

  return (
    <div className="flex grow flex-col">
      <header className="space-y-1.5 border-b px-3 py-5">
        <div className="flex items-center justify-between">
          <div className="text-center">
            <h1 className="text-2xl font-bold">Design your resume</h1>
            <p className="text-sm text-muted-foreground">
              Follow the steps below to create your resume.
            </p>
          </div>
          <Button 
            variant="outline" 
            size="sm"
            onClick={handleSave}
            disabled={isSaving || !hasUnsavedChanges}
            className="flex items-center gap-1"
          >
            <SaveIcon className="h-4 w-4" />
            <span>{isSaving ? "Saving..." : "Save"}</span>
          </Button>
        </div>
      </header>
      <main className="flex flex-col md:flex-row flex-1 overflow-hidden">
        <div
          className={cn(
            "flex-1 overflow-y-auto p-4",
            showSmResumePreview && "hidden md:block"
          )}
        >
          <div className="mx-auto max-w-2xl">
            <Breadcrumbs currentStep={currentStep} setCurrentStep={setStep} />
            {FormComponent && (
              <FormComponent
                resumeData={resumeData}
                setResumeData={setResumeData}
              />
            )}
          </div>
        </div>
        <ResumePreviewSection
          resumeData={resumeData}
          setResumeData={setResumeData}
          className={cn(
            "hidden md:flex md:w-1/2 lg:w-2/5",
            showSmResumePreview && "flex"
          )}
        />
      </main>
      <Footer
        currentStep={currentStep}
        setCurrentStep={setStep}
        showSmResumePreview={showSmResumePreview}
        setShowSmResumePreview={setShowSmResumePreview}
        isSaving={isSaving}
        handleSave={handleSave}
        hasUnsavedChanges={hasUnsavedChanges}
      />
    </div>
  );
}
