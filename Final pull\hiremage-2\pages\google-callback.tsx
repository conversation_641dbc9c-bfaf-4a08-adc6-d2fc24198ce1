import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { useDispatch } from "react-redux";
import Cookies from "js-cookie";
import { setUser } from "@/store/slice/user.slice";
import RootLoader from "@/components/RootLoader";
import BlankLayout from "@/components/Layouts/BlankLayout";
import { useTranslation } from "react-i18next";

const GoogleCallback = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const [isProcessing, setIsProcessing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleCallback = async () => {
      try {
        // Extract tokens from URL query parameters
        const { accessToken, refreshToken, user, error: urlError } = router.query;

        // Check if there's an error in the URL
        if (urlError) {
          setError(typeof urlError === 'string' ? urlError : 'Authentication failed');
          setIsProcessing(false);
          return;
        }

        // Validate that we have the required tokens
        if (!accessToken || !refreshToken) {
          setError('Missing authentication tokens. Please try logging in again.');
          setIsProcessing(false);
          return;
        }

        // Store tokens in httpOnly cookies (more secure against XSS)
        // Note: This requires server-side cookie handling
        const tokenValue = typeof accessToken === 'string' ? accessToken : accessToken[0];
        const refreshTokenValue = typeof refreshToken === 'string' ? refreshToken : refreshToken[0];
        
        // If httpOnly cookies aren't feasible, consider token rotation and shorter expiry
        Cookies.set('token', tokenValue, { secure: true, sameSite: 'strict' });
        localStorage.setItem('accessToken', tokenValue);
        localStorage.setItem('refreshToken', refreshTokenValue);
        // If user data is provided, store it
        if (user) {
          try {
            const userData = typeof user === 'string' ? JSON.parse(user) : user;
            localStorage.setItem('user', JSON.stringify(userData));
            Cookies.set('user', JSON.stringify(userData));
            Cookies.set('role', userData.role?.toString() || '');
            
            // Update Redux store
            dispatch(setUser(userData));
          } catch (parseError) {
            console.error('Error parsing user data:', parseError);
            // Continue without user data - it will be fetched later
          }
        }

        // Show success message briefly before redirect
        setTimeout(() => {
          router.push('/dashboard');
        }, 1000);

      } catch (error) {
        console.error('Google OAuth callback error:', error);
        setError('An unexpected error occurred during authentication');
        setIsProcessing(false);
      }
    };

    // Only process if router is ready and we have query parameters
    if (router.isReady) {
      handleCallback();
    }
  }, [router.isReady, router.query, dispatch]);

  // Handle error state
  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-lg dark:bg-gray-800">
          <div className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900">
              <svg
                className="h-6 w-6 text-red-600 dark:text-red-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </div>
            <h2 className="mb-2 text-xl font-semibold text-gray-900 dark:text-white">
              {t("Authentication Failed")}
            </h2>
            <p className="mb-6 text-gray-600 dark:text-gray-400">
              {error}
            </p>
            <button
              onClick={() => router.push('/login')}
              className="w-full rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              {t("Back to Login")}
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Show loading state while processing
  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-lg dark:bg-gray-800">
        <div className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100 dark:bg-green-900">
            <svg
              className="h-6 w-6 text-green-600 dark:text-green-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <h2 className="mb-2 text-xl font-semibold text-gray-900 dark:text-white">
            {t("Authentication Successful")}
          </h2>
          <p className="mb-6 text-gray-600 dark:text-gray-400">
            {t("Redirecting to dashboard...")}
          </p>
          <div className="flex justify-center">
            <RootLoader />
          </div>
        </div>
      </div>
    </div>
  );
};

GoogleCallback.getLayout = (page: any) => {
  return <BlankLayout>{page}</BlankLayout>;
};

export default GoogleCallback;
