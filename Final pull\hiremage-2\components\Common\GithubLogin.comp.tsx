import { IRootState } from "@/store";
import React from "react";
import { useSelector } from "react-redux";
import { FaGithub } from "react-icons/fa";

const GitHubLogin = () => {
  const { settings } = useSelector((state: IRootState) => state.common.data);

  const handleGitHubLogin = () => {
    if (!settings?.github_auth_client_id) return;
    const client_id = settings?.github_auth_client_id;
    const currentDomainUrl = window.location.origin;
    const redirect_uri = `${currentDomainUrl}/login`;
    const githubOAuthUrl = `https://github.com/login/oauth/authorize?client_id=${client_id}&redirect_uri=${redirect_uri}&scope=user`;

    window.location.href = githubOAuthUrl;
  };

  return (
    <button
      className="social-login-button flex w-full items-center justify-center gap-3 rounded-lg border border-gray-300 bg-white px-4 py-3 text-gray-700 shadow-sm transition-all duration-200 hover:bg-gray-50 focus:outline-none focus:ring-0 active:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:hover:bg-gray-700"
      onClick={handleGitHubLogin}
    >
      <FaGithub className="h-5 w-5 text-gray-900 dark:text-white" />
      <span className="text-sm font-medium">
        Continue with GitHub
      </span>
    </button>
  );
};

export default GitHubLogin;
