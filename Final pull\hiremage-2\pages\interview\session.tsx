import { useEffect } from "react";
import { useRouter } from "next/router";
import dynamic from "next/dynamic";
import { useDispatch } from "react-redux";
import { toggleSidebar } from "../../store/themeConfigSlice";
import { useTranslation } from "react-i18next";
import { useSubscriptionStatus } from "@/hooks/paymentSettings.hook";
import Swal from "sweetalert2";

// Dynamically import the interview session component to avoid SSR issues
const InterviewSession = dynamic(() => import("../../components/interview-session"), {
  ssr: false,
});

export default function InterviewSessionPage() {
  const router = useRouter();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { role } = router.query;
  const { data: subscriptionData, isLoading: isSubscriptionLoading } = useSubscriptionStatus();

  // Check subscription status when component mounts
  useEffect(() => {
    if (!isSubscriptionLoading && !subscriptionData?.success) {
      Swal.fire({
        title: 'Subscription Required',
        text: 'You need to subscribe to access the interview feature.',
        icon: 'info',
        confirmButtonText: 'Subscribe',
        confirmButtonColor: '#3085d6',
      }).then((result) => {
        if (result.isConfirmed) {
          router.push('/upgrade');
        } else {
          router.push('/interview');
        }
      });
      return;
    }

    document.body.classList.add('full-page-interview');
    dispatch(toggleSidebar());
    
    return () => {
      document.body.classList.remove('full-page-interview');
      dispatch(toggleSidebar());
    };
  }, [dispatch, subscriptionData, isSubscriptionLoading, router]);

  const handleExit = () => {
    router.push("/interview");
  };

  if (isSubscriptionLoading) {
    return <div>Loading...</div>;
  }

  if (!subscriptionData?.success) {
    return null;
  }

  return (
    <div className="interview-session-container h-screen w-screen bg-white dark:bg-gray-900">
      <InterviewSession onExit={handleExit} />
    </div>
  );
} 