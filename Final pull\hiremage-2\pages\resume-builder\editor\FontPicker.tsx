import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { TypeIcon } from "lucide-react";
import { useState } from "react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";

// List of recommended fonts for resumes
const resumeFonts = [
  { name: "<PERSON><PERSON><PERSON>", value: "<PERSON><PERSON><PERSON>, serif" },
  { name: "Gill Sans", value: "Gill Sans, sans-serif" },
  { name: "Cambria", value: "Cambria, serif" },
  { name: "<PERSON><PERSON><PERSON>", value: "Cal<PERSON>ri, sans-serif" },
  { name: "Constantia", value: "Constantia, serif" },
  { name: "Lato", value: "Lato, sans-serif" },
  { name: "Didot", value: "Didot, serif" },
  { name: "Helvetica", value: "Helvetica, sans-serif" },
  { name: "Georgia", value: "Georgia, serif" },
  { name: "Avenir", value: "Avenir, sans-serif" },
];

interface FontPickerProps {
  font: string | undefined;
  onChange: (font: string) => void;
}

export default function FontPicker({ font, onChange }: FontPickerProps) {
  const [showPopover, setShowPopover] = useState(false);
  
  // Find the selected font display name or use default
  const selectedFont = resumeFonts.find(f => f.value === font)?.name || "Default";

  return (
    <Popover open={showPopover} onOpenChange={setShowPopover}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="icon"
          title="Change resume font"
          onClick={() => setShowPopover(true)}
          className="relative overflow-hidden border-2"
          style={{ borderColor: "var(--border)" }}
        >
          <TypeIcon className="size-5 relative z-10 text-foreground" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="p-0 border shadow-md w-[200px]"
        align="end"
      >
        <Command>
          <CommandInput placeholder="Search fonts..." />
          <CommandEmpty>No font found.</CommandEmpty>
          <CommandGroup>
            {resumeFonts.map((font) => (
              <CommandItem
                key={font.name}
                value={font.name}
                onSelect={() => {
                  onChange(font.value);
                  setShowPopover(false);
                }}
                className="flex items-center gap-2 cursor-pointer"
              >
                <span style={{ fontFamily: font.value }}>
                  {font.name}
                </span>
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
} 