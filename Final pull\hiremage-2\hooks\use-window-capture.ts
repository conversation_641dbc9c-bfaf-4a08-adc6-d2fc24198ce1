'use client';

import { useState, useCallback } from 'react';

interface WindowStream {
  videoStream: MediaStream | null;
  audioStream: MediaStream | null;
  error: Error | null;
}

// export function useWindowCapture() {
//   const [streams, setStreams] = useState<WindowStream>({
//     videoStream: null,
//     audioStream: null,
//     error: null
//   });

//   const startCapture = useCallback(async () => {
//     try {
//       const stream = await navigator.mediaDevices.getDisplayMedia({
//         video: {
//           displaySurface: 'window'
//         },
//         audio: {
//           echoCancellation: true,
//           noiseSuppression: true,
//           autoGainControl: true,
//           channelCount: 1
//         }
//       });

//       // Separate video and audio tracks
//       const videoTracks = stream.getVideoTracks();
//       const audioTracks = stream.getAudioTracks();

//       // Create separate streams for video and audio
//       const videoStream = new MediaStream(videoTracks);
//       const audioStream = audioTracks.length > 0 ? new MediaStream(audioTracks) : null;

//       if (!audioStream) {
//         throw new Error('No audio track available. Please ensure you enable audio sharing.');
//       }

//       setStreams({
//         videoStream,
//         audioStream,
//         error: null
//       });
//     } catch (err) {
//       const error = err instanceof Error ? err :
//         new Error('Failed to capture window. Please ensure you grant both video and audio permissions.');
      
//       setStreams({
//         videoStream: null,
//         audioStream: null,
//         error
//       });
//       throw error;
//     }
//   }, []);

//   const stopCapture = useCallback(() => {
//     streams.videoStream?.getTracks().forEach(track => track.stop());
//     streams.audioStream?.getTracks().forEach(track => track.stop());
//     setStreams({
//       videoStream: null,
//       audioStream: null,
//       error: null
//     });
//   }, [streams]);

//   return {
//     ...streams,
//     startCapture,
//     stopCapture
//   };
// }