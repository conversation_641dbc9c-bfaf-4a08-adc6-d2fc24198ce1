import { del } from "@vercel/blob";
import apiClient from "@/lib/api-client";

export async function deleteResume(id: string, photoUrl?: string) {
  try {
    // Delete photo from Vercel Blob if it exists
    if (photoUrl && photoUrl.includes("vercel-storage.com")) {
      try {
        await del(photoUrl);
      } catch (error) {
        console.error("Failed to delete photo:", error);
      }
    }

    // Delete the resume from the database
    await apiClient.deleteResume(id);
    
    // Return success response
    return { success: true };
  } catch (error) {
    console.error("Error deleting resume:", error);
    return { success: false, error };
  }
}
