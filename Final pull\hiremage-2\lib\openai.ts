import OpenAI from "openai";

// In Next.js, the environment variables are automatically loaded from .env and .env.local
// If the key is not found, provide a more helpful error message
if (!process.env.OPENAI_API_KEY) {
  console.warn("Warning: OPENAI_API_KEY not found in environment variables");
  console.warn("Please add your OpenAI API key to .env file");
}

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || "",
});

export default openai;
