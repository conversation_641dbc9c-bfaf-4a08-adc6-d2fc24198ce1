import { ResumeValues, WorkExperience } from "./validation";
import <PERSON><PERSON> from "js-cookie";

// Interface with ID for database operations
interface WorkExperienceWithId extends WorkExperience {
  id?: string;
}

interface EducationWithId {
  id?: string;
  degree?: string;
  school?: string;
  startDate?: string;
  endDate?: string;
}

// Get headers for fetch requests
function getHeaders() {
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "apisecretkeycheck": process.env.NEXT_PUBLIC_API_SECRET || ""
  };

  const token = Cookie.get("token");
  if (token) {
    headers["Authorization"] = `Bearer ${token}`;
  }

  return headers;
}

// Convert dates from strings to Date objects for Prisma
function convertDatesToISOString(obj: any): any {
  if (!obj) return obj;
  
  // Handle arrays
  if (Array.isArray(obj)) {
    return obj.map(item => convertDatesToISOString(item));
  }
  
  // Handle objects
  if (typeof obj === 'object' && !(obj instanceof Date)) {
    const newObj: any = {};
    
    for (const key in obj) {
      // Skip null or undefined values to avoid issues
      if (obj[key] == null) continue;
      
      // Convert Date objects to ISO strings
      if (obj[key] instanceof Date) {
        newObj[key] = obj[key].toISOString();
      } 
      // Handle date strings
      else if (key.toLowerCase().includes('date') && typeof obj[key] === 'string') {
        try {
          const date = new Date(obj[key]);
          if (!isNaN(date.getTime())) {
            newObj[key] = date.toISOString();
          } else {
            newObj[key] = obj[key];
          }
        } catch {
          newObj[key] = obj[key];
        }
      }
      // Recursively process nested objects and arrays
      else if (typeof obj[key] === 'object') {
        newObj[key] = convertDatesToISOString(obj[key]);
      } 
      // Keep other values as is
      else {
        newObj[key] = obj[key];
      }
    }
    
    return newObj;
  }
  
  // Return non-object values as is
  return obj instanceof Date ? obj.toISOString() : obj;
}

// Backend API client for resume operations
const apiClient = {
  // Get all resumes
  getResumes: async () => {
    console.log('API URL:', process.env.NEXT_PUBLIC_BASE_URL);
    const url = `${process.env.NEXT_PUBLIC_BASE_URL}/resume-builder/resumes`;
    console.log('Fetching resumes from:', url);
    
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: getHeaders()
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.message || `Failed with status: ${response.status}`);
      }
      
      const resumes = await response.json();
      return resumes;
    } catch (error: any) {
      console.error('Error fetching resumes:', error);
      throw error;
    }
  },

  // Get a resume by ID
  getResumeById: async (id: string) => {
    try {
      const url = `${process.env.NEXT_PUBLIC_BASE_URL}/resume-builder/resumes/${id}`;
      console.log('Fetching resume by ID from:', url);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: getHeaders()
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.message || `Failed with status: ${response.status}`);
      }
      
      const resume = await response.json();
      return resume;
    } catch (error: any) {
      console.error('Error fetching resume:', error);
      // If the resume is not found, return a minimal object with the ID
      if (error.message?.includes('not found')) {
        return { id };
      }
      throw error;
    }
  },

  // Create a new resume
  createResume: async (data: Partial<ResumeValues>) => {
    const { workExperiences, educations, ...resumeData } = data;
    
    // Explicit creation of array values that won't be converted to objects
    const workExpArray = Array.isArray(workExperiences) 
      ? workExperiences.map(exp => ({
          position: exp.position || '',
          company: exp.company || '',
          startDate: exp.startDate ? new Date(exp.startDate).toISOString() : null,
          endDate: exp.endDate ? new Date(exp.endDate).toISOString() : null,
          description: exp.description || ''
        }))
      : [];
      
    const educationsArray = Array.isArray(educations)
      ? educations.map(edu => ({
          degree: edu.degree || '',
          school: edu.school || '',
          startDate: edu.startDate ? new Date(edu.startDate).toISOString() : null,
          endDate: edu.endDate ? new Date(edu.endDate).toISOString() : null
        }))
      : [];
      
    const skillsArray = Array.isArray(data.skills)
      ? data.skills.filter(skill => typeof skill === 'string')
      : [];
    
    // Create proper arrays for all collections
    const formattedData = {
      ...resumeData,
      workExperiences: workExpArray,
      educations: educationsArray,
      skills: skillsArray
    };

    // Convert dates
    const finalData = convertDatesToISOString(formattedData);

    const url = `${process.env.NEXT_PUBLIC_BASE_URL}/resume-builder/resumes`;
    console.log('Creating resume at:', url);
    console.log('Data being sent:', JSON.stringify(finalData, null, 2));
    
    // Force JSON to stringify arrays properly
    const bodyString = JSON.stringify(finalData);
    
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: getHeaders(),
        body: bodyString
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.message || `Failed with status: ${response.status}`);
      }
      
      const result = await response.json();
      console.log('Resume created successfully:', result);
      return result;
    } catch (error: any) {
      console.error('Error creating resume:', error);
      throw error;
    }
  },

  // Update an existing resume
  updateResume: async (id: string, data: Partial<ResumeValues>) => {
    try {
      // Make a copy of data and remove the id to avoid including it in the request body
      const dataCopy = { ...data };
      if ('id' in dataCopy) {
        delete dataCopy.id;
      }
      
      const { workExperiences, educations, ...resumeData } = dataCopy;
      
      // Cast to extended types that include ID
      const workExperiencesWithId = workExperiences as WorkExperienceWithId[] | undefined;
      const educationsWithId = educations as EducationWithId[] | undefined;
      
      // Explicit creation of array values that won't be converted to objects
      const workExpArray = Array.isArray(workExperiencesWithId)
        ? workExperiencesWithId.map(exp => ({
            ...(exp.id && { id: exp.id }),
            position: exp.position || '',
            company: exp.company || '',
            startDate: exp.startDate ? new Date(exp.startDate).toISOString() : null,
            endDate: exp.endDate ? new Date(exp.endDate).toISOString() : null,
            description: exp.description || ''
          }))
        : [];
        
      const educationsArray = Array.isArray(educationsWithId)
        ? educationsWithId.map(edu => ({
            ...(edu.id && { id: edu.id }),
            degree: edu.degree || '',
            school: edu.school || '',
            startDate: edu.startDate ? new Date(edu.startDate).toISOString() : null,
            endDate: edu.endDate ? new Date(edu.endDate).toISOString() : null
          }))
        : [];
        
      const skillsArray = Array.isArray(dataCopy.skills)
        ? dataCopy.skills.filter(skill => typeof skill === 'string')
        : [];
      
      // Create proper arrays for all collections
      const formattedData = {
        ...resumeData,
        workExperiences: workExpArray,
        educations: educationsArray,
        skills: skillsArray
      };

      // Convert dates
      const finalData = convertDatesToISOString(formattedData);
      
      const url = `${process.env.NEXT_PUBLIC_BASE_URL}/resume-builder/resumes/${id}`;
      console.log('Updating resume at:', url);
      console.log('Data being sent:', JSON.stringify(finalData, null, 2));
      
      // Force JSON to stringify arrays properly
      const bodyString = JSON.stringify(finalData);
      
      const response = await fetch(url, {
        method: 'PUT',
        headers: getHeaders(),
        body: bodyString
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.message || `Failed with status: ${response.status}`);
      }
      
      const result = await response.json();
      console.log('Resume updated successfully:', result);
      return result;
    } catch (error: any) {
      console.error('Error updating resume:', error);
      
      // If the resume is not found, try creating a new one with this ID
      if (error.message?.includes('not found')) {
        console.log('Resume not found, creating a new resume instead');
        return apiClient.createResume({ ...data, id });
      }
      
      throw error;
    }
  },

  // Delete a resume
  deleteResume: async (id: string) => {
    try {
      const url = `${process.env.NEXT_PUBLIC_BASE_URL}/resume-builder/resumes/${id}`;
      console.log('Deleting resume at:', url);
      
      const response = await fetch(url, {
        method: 'DELETE',
        headers: getHeaders()
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.message || `Failed with status: ${response.status}`);
      }
      
      return true;
    } catch (error: any) {
      console.error('Error deleting resume:', error);
      
      // If the resume is not found, consider it already deleted
      if (error.message?.includes('not found')) {
        console.log('Resume not found, considering it already deleted');
        return true;
      }
      
      throw error;
    }
  }
};

export default apiClient; 