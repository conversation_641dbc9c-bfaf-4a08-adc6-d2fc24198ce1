import React from "react";
import { useTranslation } from "react-i18next";
import { IRootState } from "@/store";
import { useSelector } from "react-redux";

const Credits = () => {
  const { t } = useTranslation();
  const { details: subscriptionDetails } = useSelector(
    (state: IRootState) => state.subcription
  );

  const calculatePercent = (value: any) => {
    let totalValue = subscriptionDetails?.total_words;
    return (value / totalValue) * 100;
  };

  return (
    <div className="px-4 pt-2">
      <div className="border p-2.5 dark:border-dark dark:bg-black">
        <h5 className="text-black dark:text-white">
          {t("Credits")} Information
        </h5>
        <div className="my-2 flex flex-col gap-3">
          <div className="font-bold text-black dark:text-white">
            {t("Words")} {subscriptionDetails?.remaining_words}
          </div>
          <div className="h-2 overflow-hidden rounded-full bg-[#ebedf2] dark:bg-dark">
            <div
              className={`h-full rounded-full bg-gradient-to-r from-[#4361ee] to-[#805dca]`}
              style={{
                width: `${calculatePercent(subscriptionDetails?.total_words)}%`,
              }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Credits;
