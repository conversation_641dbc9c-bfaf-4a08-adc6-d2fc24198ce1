import { EditorFormProps } from "@/lib/types";
import EducationForm from "@/pages/resume-builder/editor/forms/EducationForm";
import GeneralInfoForm from "@/pages/resume-builder/editor/forms/GeneralInfoForm";
import PersonalInfoForm from "@/pages/resume-builder/editor/forms/PersonalInfoForm";
import SkillsForm from "@/pages/resume-builder/editor/forms/SkillsForm";
import SummaryForm from "@/pages/resume-builder/editor/forms/SummaryForm";
import WorkExperienceForm from "@/pages/resume-builder/editor/forms/WorkExperienceForm";

export const steps: {
  title: string;
  component: React.ComponentType<EditorFormProps>;
  key: string;
}[] = [
  { title: "General info", component: GeneralInfoForm, key: "general-info" },
  { title: "Personal info", component: PersonalInfoForm, key: "personal-info" },
  {
    title: "Work experience",
    component: WorkExperienceForm,
    key: "work-experience",
  },
  {
    title: "Education",
    component: EducationForm,
    key: "education",
  },
  {
    title: "Skills",
    component: SkillsForm,
    key: "skill",
  },
  {
    title: "Summary",
    component: SummaryForm,
    key: "summary",
  },
];
