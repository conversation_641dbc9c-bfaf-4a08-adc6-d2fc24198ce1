"use client";

import { useRouter } from "next/router";
import ResumeEditor from "./ResumeEditor";

export default function Page() {
  const router = useRouter();
  const { resumeId } = router.query;
  
  // Mock implementation - in a real app, you would fetch from your database here
  const resumeToEdit = resumeId ? { id: resumeId as string } : null;

  return <ResumeEditor resumeToEdit={resumeToEdit} />;
}
