"use client";

import DefaultLayout from "@/components/Layouts/DefaultLayout";
import { useState } from "react";
import { HiOutlineInformationCircle } from "react-icons/hi2";
import Link from "next/link";
import { PiSquaresFourLight } from "react-icons/pi";

const MAX_CHARS = 10000;

export default function AiPersonalise() {
  const [context, setContext] = useState("");

  const handleClear = () => setContext("");

  return (
    <div className="min-h-screen bg-[#f5f6fa] flex flex-col">
      {/* Header & Breadcrumbs */}
      <div className="w-full flex flex-col items-center pt-6 pb-4">
        <div className="w-full max-w-5xl text-xs sm:text-sm text-gray-500 mb-2 px-4 text-right">
          <Link href="/" className="hover:underline text-[#1a2240]">Home</Link>
          <span className="mx-1">/</span>
          <span className="text-[#2563eb] font-medium">Personalize AI</span>
        </div>
        <h1 className="w-full max-w-5xl text-xl sm:text-2xl md:text-3xl font-bold text-[#1a2240] text-left px-4 mb-6">
          Personalize AI
        </h1>
      </div>

      {/* Main Content */}
      <div className="w-full flex justify-center px-2 sm:px-4">
        <div className="w-full max-w-6xl bg-white rounded-2xl shadow-lg px-4 sm:px-6 md:px-10 py-6 sm:py-10">
          {/* Icon & Title */}
          <div className="flex items-center gap-3 sm:gap-4 mb-4">
            <div className="rounded-lg bg-[#f4f3ff] p-2 sm:p-3 flex items-center justify-center">
              <PiSquaresFourLight className="text-[#2563eb]" size={28} />
            </div>
            <span className="text-lg sm:text-xl md:text-2xl font-bold text-[#1a2240]">
              Personalize AI
            </span>
          </div>

          <p className="text-gray-500 text-sm sm:text-base mb-6">
            Provide context about yourself, your preferences, work style, or any specific information you'd like AI services to consider when generating responses tailored to you.
          </p>

          {/* Example Box */}
          <div className="bg-[#f5f6fa] border border-[#e5e7eb] rounded-lg p-3 sm:p-4 text-sm sm:text-base text-gray-700 mb-6">
            <span className="font-bold text-gray-800">Example: </span>
            "I'm a software developer specializing in React and Next.js. I prefer TypeScript, functional programming patterns, and clean, well-documented code. When explaining concepts, use practical examples and focus on best practices."
          </div>

          {/* AI Context Label */}
          <label htmlFor="ai-context" className="block font-medium mb-2 text-sm sm:text-base text-gray-900">
            AI Context<span className="text-red-500">*</span>
          </label>

          {/* Textarea Input */}
          <div className="relative mb-6">
            <textarea
              id="ai-context"
              className="w-full min-h-[180px] max-h-[300px] border border-gray-300 rounded-lg p-4 text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm resize-y placeholder:text-gray-400"
              placeholder="Describe yourself, your preferences, work style, or specific context you want AI to remember..."
              value={context}
              maxLength={MAX_CHARS}
              onChange={(e) => setContext(e.target.value)}
            />
            <div className="absolute bottom-3 right-4 text-xs text-gray-400">
              {context.length.toLocaleString()}/{MAX_CHARS.toLocaleString()}
            </div>
          </div>

          {/* Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <button
              type="button"
              className="w-full sm:w-auto px-6 bg-[#19213d] hover:bg-[#232b4d] text-white py-3 rounded-lg font-semibold text-base transition"
            >
              Save AI Context
            </button>
            <button
              type="button"
              className="w-full sm:w-auto px-6 bg-white border border-gray-300 hover:bg-gray-100 text-[#19213d] py-3 rounded-lg font-semibold text-base transition"
              onClick={handleClear}
            >
              Clear
            </button>
          </div>

          {/* Info Box */}
          <div className="bg-[#eaf2fe] text-[#1a2240] p-3 sm:p-5 rounded-lg mt-2 text-sm sm:text-base flex items-start gap-3">
            <HiOutlineInformationCircle className="text-[#2563eb] mt-1" size={20} />
            <div>
              <span className="font-semibold block mb-1">How this works</span>
              <span className="text-[#2563eb]">
                Your AI context will be securely stored and used by AI services to provide more personalized and relevant responses. You can update or clear this information at any time.
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="w-full flex justify-center items-center py-6 mt-10 text-gray-400 text-xs sm:text-sm">
        © {new Date().getFullYear()} HireMage. All rights reserved.
      </footer>
    </div>
  );
}
