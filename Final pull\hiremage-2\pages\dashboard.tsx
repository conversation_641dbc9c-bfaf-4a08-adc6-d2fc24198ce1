import Link from "next/link";
import { useDispatch } from "react-redux";
import { useEffect } from "react";
import { setPageTitle } from "../store/themeConfigSlice";
import ProgressCardWithLimit from "@/components/Cards/ProgressCardWithLimit.comp";
import InfoCard from "@/components/Cards/infoCard.comp";
import { useGetUserDashboardData } from "@/hooks/user.hook";
import moment from "moment";
import NoItemFound from "@/components/Common/NoItemFound.comp";
import { FaFileAlt } from "react-icons/fa";
import SectionLoader from "@/components/SectionLoader";
import { useTranslation } from "react-i18next";
const Dashboard = () => {
  const { data, isLoading } = useGetUserDashboardData();
  const { t } = useTranslation();

  if (isLoading)
    return (
      <div className="container min-h-screen pt-5">
        <SectionLoader />
      </div>
    );

  return (
    <div className="container min-h-screen pt-5">
      <div className="mb-6 grid gap-6 px-6 sm:grid-cols-2 lg:grid-cols-3">
        <InfoCard total_documents={data?.total_documents} />
        <ProgressCardWithLimit
          title="Words Left"
          number={data?.word_left}
          subtext="WORDS"
          sideText={`${data?.total_words}`}
          percentage={
            ((data?.total_words - data?.word_left) / data?.total_words) * 100
          }
        />
      </div>

      <div className="grid grid-cols-1 gap-4 px-6 lg:grid-cols-2">
        <div className="panel h-full">
          <div className="-mx-5 mb-5 flex items-start justify-between border-b border-white-light p-5 pt-0  dark:border-[#1b2e4b] dark:text-white">
            <h5 className="text-lg font-semibold ">My Favourite's</h5>
          </div>

          <div className="table-responsive">
            <table className="w-full table-auto">
              <tbody>
                {data?.favourite_template_list?.map((item: any) => (
                  <tr key={item?.id}>
                    <td className={`!p-2`}>
                      <div className="flex items-center space-x-2">
                        <div
                          className={`rounded-md p-2`}
                          style={{
                            color: item?.template?.color,
                          }}
                        >
                          {item?.template?.icon_tag ? (
                            <i
                              className={`${item?.template?.icon_tag} text-2xl`}
                            ></i>
                          ) : (
                            <FaFileAlt
                              size={20}
                              color={item?.template?.color}
                            />
                          )}{" "}
                        </div>
                      </div>
                    </td>
                    <td className="!p-2">
                      <span className="flex items-center text-xs font-semibold capitalize text-gray-800 dark:text-white md:text-base">
                        {item?.template?.title}
                      </span>
                    </td>
                    <td className="!p-2">
                      <span className="flex items-center text-xs text-gray-500 dark:text-white md:text-sm">
                        {item?.template?.prompt.slice(0, 30)}
                        {item?.template?.prompt.length > 30 ? "..." : ""}
                      </span>
                    </td>
                    <td className="!p-2">
                      <button className="rounded-md border px-5 py-2 dark:border-dark lg:block">
                        Generate
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {data?.favourite_template_list?.length <= 0 && (
              <NoItemFound message="No Favourite Template Found" />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;

export async function getServerSideProps(context: any) {
  const { req } = context;
  const { token } = req.cookies;

  if (!token) {
    return {
      redirect: {
        destination: "/login",
        permanent: false,
      },
    };
  }

  return {
    props: {},
  };
}
