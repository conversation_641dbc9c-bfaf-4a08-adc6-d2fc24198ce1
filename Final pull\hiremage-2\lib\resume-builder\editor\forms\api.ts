import {
  GenerateSummaryInput,
  GenerateWorkExperienceInput,
  WorkExperience,
} from "@/lib/validation";

export async function generateSummary(input: GenerateSummaryInput) {
  const response = await fetch("/api/generate-summary", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(input),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to generate summary");
  }

  const data = await response.json();
  return data.summary;
}

export async function generateWorkExperience(
  input: GenerateWorkExperienceInput
) {
  const response = await fetch("/api/generate-work-experience", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(input),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to generate work experience");
  }

  return await response.json() as WorkExperience;
} 