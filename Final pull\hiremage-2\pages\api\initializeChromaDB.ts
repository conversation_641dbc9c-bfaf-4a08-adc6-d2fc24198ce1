import type { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { resumeId, sessionId, jobDescription, selectedRole, interviewType } = req.body;

    if (!resumeId || !sessionId) {
      return res.status(400).json({ error: 'resumeId and sessionId are required' });
    }

    // Forward the request to the chatbot backend
    // Use the CHATBOT_API_URL environment variable or default to a reasonable endpoint
    const backendUrl = process.env.NEXT_PUBLIC_CHATBOT_API_URL || 'http://localhost:8001';
    console.log(`Connecting to chatbot backend at: ${backendUrl}`);
    
    // First check if the backend is accessible
    try {
      const testResponse = await fetch(`${backendUrl}/api/test`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (testResponse.ok) {
        console.log('Backend API is accessible');
      } else {
        console.error(`Backend API test failed with status: ${testResponse.status}`);
        return res.status(503).json({ 
          error: `Cannot connect to backend API at ${backendUrl}`, 
          status: testResponse.status,
          details: await testResponse.text().catch(() => 'No response text')
        });
      }
    } catch (testError) {
      console.error('Error connecting to backend API:', testError);
      return res.status(503).json({ 
        error: `Network error connecting to backend API at ${backendUrl}`, 
        details: testError instanceof Error ? testError.message : String(testError)
      });
    }
    
    // Now proceed with the actual request
    const response = await fetch(`${backendUrl}/api/initialize-chromadb`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        resumeId,
        sessionId,
        jobDescription,
        selectedRole,
        interviewType
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: `HTTP error! status: ${response.status}` }));
      console.error('Error response from backend:', errorData);
      return res.status(response.status).json(errorData);
    }

    const data = await response.json();
    return res.status(200).json(data);
  } catch (error) {
    console.error('Error initializing ChromaDB:', error);
    return res.status(500).json({ 
      error: 'Failed to initialize ChromaDB',
      details: error instanceof Error ? error.message : String(error)
    });
  }
} 