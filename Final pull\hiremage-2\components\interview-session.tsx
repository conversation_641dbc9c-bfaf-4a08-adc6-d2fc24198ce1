'use client';

import { useState, useEffect, useRef } from 'react';
import { ScrollArea } from './ui/scroll-area';
import WindowCapture from './window-capture';
import { useAIResponse } from '@/hooks/use-ai-response';
import { GripVertical, Copy, Download, Send, X, LogOut, Trash2 } from 'lucide-react';
import { Switch } from './ui/switch';
import { toast } from 'react-toastify';
import copy from 'copy-to-clipboard';
import Prism from 'prismjs';
import 'prismjs/themes/prism-tomorrow.css';
import 'prismjs/components/prism-javascript';
import 'prismjs/components/prism-typescript';
import 'prismjs/components/prism-python';
import 'prismjs/components/prism-bash';
import 'prismjs/components/prism-sql';
import 'prismjs/components/prism-json';

// Add styles to hide footer
const hideFooterStyles = `
  body.full-page-interview footer,
  body.full-page-interview .footer,
  body.full-page-interview .copyright,
  body.full-page-interview .minimal-footer {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    position: absolute !important;
    z-index: -1 !important;
  }
  
  @keyframes fadeInDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

interface Message {
  id: string;
  text: string;
  timestamp: Date;
  type: 'ai';
  isCode?: boolean;
  transcription: string;
}

interface InterviewSessionProps {
  resumeId?: string;
  jobDescription?: string;
  selectedRole?: string;
  onExit?: () => void;
}

// Component for code block with syntax highlighting
function CodeBlock({ code, language = 'javascript' }: { code: string, language?: string }) {
  useEffect(() => {
    // Only highlight if Prism is available (client-side)
    if (typeof window !== 'undefined') {
      Prism.highlightAll();
    }
  }, [code]);

  const copyToClipboard = async () => {
    try {
      copy(code);
      toast.success("Code Copied!");
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  const downloadCode = () => {
    const blob = new Blob([code], { type: "text/plain" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `code-${Date.now()}.${language === 'javascript' ? 'js' : language}`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="relative rounded-lg overflow-hidden border border-zinc-700 my-2">
      <div className="flex items-center justify-between px-3 py-2 bg-zinc-800">
        <span className="text-xs text-zinc-400">{language}</span>
        <div className="flex items-center gap-1">
          <button
            onClick={copyToClipboard}
            className="p-1 hover:bg-zinc-700 rounded transition-colors"
            title="Copy code"
          >
            <Copy className="h-4 w-4 text-zinc-400" />
          </button>
          <button
            onClick={downloadCode}
            className="p-1 hover:bg-zinc-700 rounded transition-colors"
            title="Download code"
          >
            <Download className="h-4 w-4 text-zinc-400" />
          </button>
        </div>
      </div>
      <pre className="p-4 overflow-x-auto bg-zinc-900 m-0">
        <code className={`language-${language}`}>{code}</code>
      </pre>
    </div>
  );
}

export default function InterviewSession({ resumeId, jobDescription, selectedRole, onExit }: InterviewSessionProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [autoScroll, setAutoScroll] = useState(true);
  const { generateResponse, isLoading, setActiveResumeId } = useAIResponse();
  const [sessionId] = useState<string>(() => `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`);
  const [chromaInitialized, setChromaInitialized] = useState(false);
  const [userInput, setUserInput] = useState('');
  
  const [leftPanelWidth, setLeftPanelWidth] = useState(33);
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const transcriptionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const currentTranscriptionRef = useRef<string>('');
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  
  useEffect(() => {
    if (resumeId && !chromaInitialized) {
      console.log('Setting active resumeId in Interview Session:', resumeId);
      setActiveResumeId(resumeId);
      setChromaInitialized(true); // Mark as initialized so we don't try again
      
      // Initialize ChromaDB for this session at startup
      let retryCount = 0;
      const maxRetries = 3; // Maximum number of retries
      
      const initializeChromaDB = async () => {
        try {
          console.log('Initializing ChromaDB for resumeId:', resumeId, 'attempt:', retryCount + 1);
          const response = await fetch('/api/initializeChromaDB', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              resumeId,
              sessionId,
              jobDescription,
              selectedRole,
            }),
          });
          
          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || `Failed to initialize ChromaDB: ${response.status}`);
          }
          
          const data = await response.json();
          console.log('ChromaDB initialized successfully:', data);
          
          // Success - show a success message
          toast.success('Interview data initialized successfully');
        } catch (error) {
          console.error('Error initializing ChromaDB:', error);
          
          // Retry logic
          retryCount++;
          if (retryCount < maxRetries) {
            // Show warning message to user
            toast.warning(`Initializing interview data, attempt ${retryCount + 1}/${maxRetries}...`);
            
            // Retry after delay (increasing with each attempt)
            const retryDelay = 3000 * (retryCount); // 3s, 6s, 9s...
            setTimeout(() => {
              console.log(`Retrying ChromaDB initialization... (${retryCount}/${maxRetries})`);
              initializeChromaDB();
            }, retryDelay);
          } else {
            // Max retries reached - show error
            toast.error('Failed to initialize interview data after multiple attempts. Please try again later.');
          }
        }
      };
      
      initializeChromaDB();
    }
  }, [resumeId, setActiveResumeId, sessionId, chromaInitialized, jobDescription, selectedRole]);
  
  useEffect(() => {
    document.body.classList.add('full-page-interview');
    
    const styleEl = document.createElement('style');
    styleEl.textContent = hideFooterStyles;
    document.head.appendChild(styleEl);
    
    return () => {
      document.body.classList.remove('full-page-interview');
      document.head.removeChild(styleEl);
    };
  }, []);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing || !containerRef.current) return;
      
      const containerRect = containerRef.current.getBoundingClientRect();
      const containerWidth = containerRect.width;
      
      let newWidth = ((e.clientX - containerRect.left) / containerWidth) * 100;
      
      newWidth = Math.max(30, Math.min(80, newWidth));
      
      setLeftPanelWidth(newWidth);
      
      e.preventDefault();
    };
    
    const handleMouseUp = () => {
      if (isResizing) {
        setIsResizing(false);
        document.body.classList.remove('resizing');
      }
    };
    
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.classList.add('resizing');
    }
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing]);

  const handleResizeStart = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);
  };

  const scrollToTop = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  };
  
  useEffect(() => {
    if (autoScroll && messages.length > 0) {
      scrollToTop();
    }
  }, [messages, autoScroll]);

  const handleTranscript = async (transcript: string) => {
    if (!transcript.trim()) return;
    
    try {
      const response = await generateResponse(transcript);
      if (response) {
        let cleanedContent = response.content;
        
        cleanedContent = cleanedContent.replace(/^(Interviewer|Candidate):\s*/gm, '');
        
        const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
        const hasCodeBlocks = codeBlockRegex.test(cleanedContent);
        
        if (hasCodeBlocks) {
          cleanedContent = cleanedContent.replace(codeBlockRegex, (match, lang, code) => {
            const language = lang || 'javascript';
            return `[CODE_BLOCK:${language}]${code}[/CODE_BLOCK]`;
          });
        }
        
        // Add AI response message with transcription
        const aiMessage: Message = {
          id: Date.now().toString(),
          text: cleanedContent,
          timestamp: new Date(),
          type: 'ai',
          isCode: hasCodeBlocks,
          transcription: transcript
        };
        
        setMessages(prev => [...prev, aiMessage]);
      }
    } catch (error) {
      console.error('Error generating response:', error);
    }
  };

  const handleUserInputSubmit = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    
    if (!userInput.trim()) return;
    
    try {
      const response = await generateResponse(userInput);
      if (response) {
        let cleanedContent = response.content;
        
        cleanedContent = cleanedContent.replace(/^(Interviewer|Candidate):\s*/gm, '');
        
        const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
        const hasCodeBlocks = codeBlockRegex.test(cleanedContent);
        
        if (hasCodeBlocks) {
          cleanedContent = cleanedContent.replace(codeBlockRegex, (match, lang, code) => {
            const language = lang || 'javascript';
            return `[CODE_BLOCK:${language}]${code}[/CODE_BLOCK]`;
          });
        }
        
        // Add AI response message with manual input
        const aiMessage: Message = {
          id: Date.now().toString(),
          text: cleanedContent,
          timestamp: new Date(),
          type: 'ai',
          isCode: hasCodeBlocks,
          transcription: userInput
        };
        
        setMessages(prev => [...prev, aiMessage]);
        setUserInput(''); // Clear input field after submission
      }
    } catch (error) {
      console.error('Error generating response:', error);
    }
  };

  const renderMessageContent = (message: Message) => {
    if (!message.isCode) {
      return <p className="text-sm">{message.text}</p>;
    }

    const parts = [];
    const codeBlockPattern = /\[CODE_BLOCK:(\w+)\]([\s\S]*?)\[\/CODE_BLOCK\]/g;
    let lastIndex = 0;
    let match;

    while ((match = codeBlockPattern.exec(message.text)) !== null) {
      if (match.index > lastIndex) {
        parts.push({
          type: 'text',
          content: message.text.substring(lastIndex, match.index),
          id: `text-${lastIndex}`
        });
      }
      
      parts.push({
        type: 'code',
        language: match[1],
        content: match[2],
        id: `code-${match.index}`
      });
      
      lastIndex = match.index + match[0].length;
    }
    
    if (lastIndex < message.text.length) {
      parts.push({
        type: 'text',
        content: message.text.substring(lastIndex),
        id: `text-${lastIndex}`
      });
    }
    
    return (
      <div>
        {parts.map(part => 
          part.type === 'text' ? (
            <p key={part.id} className="text-sm mb-2">{part.content}</p>
          ) : (
            <CodeBlock 
              key={part.id} 
              code={part.content} 
              language={part.language}
            />
          )
        )}
      </div>
    );
  };

  // Add a function to clear all messages
  const clearAllMessages = () => {
    setMessages([]);
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = 0;
    }
    toast.success('All responses cleared');
  };

  return (
    <div className="interview-session-container h-screen w-screen flex items-center justify-center">
      <div 
        ref={containerRef}
        className="flex flex-row h-full"
        style={{ 
          borderRadius: '8px',
          overflow: 'hidden',
          height: 'calc(100vh - 24px)',
          width: 'calc(100vw - 24px)',
          maxHeight: '100vh',
          boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)'
        }}
      >
        <div 
          className="flex flex-col h-full"
          style={{ 
            width: `${leftPanelWidth}%`,
            minWidth: '30%',
            maxWidth: '80%',
            backgroundColor: 'white'
          }}
        >
          <h2 className="text-sm font-medium p-4 pb-0">Interview Capture</h2>
          <div className="flex-1 overflow-hidden">
            <WindowCapture onTranscript={handleTranscript} />
          </div>
        </div>
        
        <div
          className="resizer-handle h-full"
          onMouseDown={handleResizeStart}
          style={{
            width: '8px',
            backgroundColor: isResizing ? '#3f51b5' : 'transparent',
            cursor: 'col-resize',
            position: 'relative',
            zIndex: 10
          }}
        >
          <div 
            className={`resizer-grip ${isResizing ? 'active' : ''}`}
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              color: isResizing ? 'white' : '#888',
            }}
          >
            <GripVertical size={16} />
          </div>
        </div>
        
        <div 
          className="right-panel h-full flex flex-col"
          style={{ 
            width: `calc(100% - ${leftPanelWidth}% - 8px)`,
            minWidth: '20%'
          }}
        >
          <div className="flex items-center justify-between p-4 pb-0">
            <div className="flex items-center gap-2">
              <h2 className="text-sm font-medium">Interview Copilot</h2>
            </div>
            <div className="flex items-center gap-4">
              {onExit && (
                <button
                  onClick={onExit}
                  className="flex items-center gap-1 px-2 py-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
                  title="Exit Interview"
                >
                  <LogOut className="h-4 w-4" />
                  <span className="text-xs font-medium">Exit</span>
                </button>
              )}
              <button
                onClick={clearAllMessages}
                className="flex items-center gap-1 px-2 py-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                title="Clear All Responses"
                disabled={messages.length === 0}
              >
                <Trash2 className="h-4 w-4" />
                <span className="text-xs font-medium">Clear</span>
              </button>
              <div className="flex items-center gap-2">
                <span className="text-xs text-muted-foreground">Auto Scroll</span>
                <Switch 
                  checked={autoScroll} 
                  onCheckedChange={setAutoScroll}
                  className="data-[state=checked]:bg-primary/90 data-[state=unchecked]:bg-gray-200"
                  thumbClassName="data-[state=checked]:bg-white data-[state=unchecked]:bg-gray-600 transition-colors"
                />
              </div>
            </div>
          </div>
          
          <div className="flex flex-col flex-1 overflow-hidden">
            <ScrollArea className="flex-1 overflow-y-auto">
              <div className="p-4 space-y-3" ref={messagesContainerRef}>
                {messages.length === 0 && !isLoading && (
                  <div className="text-center py-8 text-gray-500">
                    <p>No responses yet</p>
                    <p className="text-xs mt-2">Start speaking or type below to begin the interview</p>
                  </div>
                )}
                
                {isLoading && (
                  <div className="p-3 rounded-md bg-muted animate-pulse transition-all duration-300 ease-in-out">
                    <p className="text-sm">Generating response...</p>
                  </div>
                )}
                
                {[...messages].reverse().map((message) => (
                  <div
                    key={message.id}
                    className="p-3 rounded-md bg-primary/10 text-primary transition-all duration-300 ease-in-out"
                    style={{
                      opacity: 1,
                      transform: 'translateY(0)',
                      animation: 'fadeInDown 0.3s ease-out'
                    }}
                  >
                    <div className="mb-2">
                      <p className="text-xs font-semibold text-muted-foreground">Question:</p>
                      <p className="text-sm italic">{message.transcription}</p>
                    </div>
                    <div className="mb-2">
                      <p className="text-xs font-semibold text-muted-foreground">Response:</p>
                      {renderMessageContent(message)}
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {message.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>
            
            {/* User Input Form - Fixed at bottom */}
            <form 
              onSubmit={handleUserInputSubmit}
              className="p-3 border-t border-gray-200 bg-white flex gap-2 m-3 mt-auto rounded-lg shrink-0"
            >
              <input
                ref={inputRef}
                type="text"
                value={userInput}
                onChange={(e) => setUserInput(e.target.value)}
                placeholder="Type your question here..."
                className="flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50"
                disabled={isLoading}
              />
              <button
                type="submit"
                className="bg-primary hover:bg-primary/90 text-white p-2 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={isLoading || !userInput.trim()}
              >
                <Send size={18} />
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}