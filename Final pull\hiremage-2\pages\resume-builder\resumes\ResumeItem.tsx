"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import { ResumeValues } from "@/lib/validation";
import { MoreVertical, Pencil, Trash2 } from "lucide-react";
import dynamic from "next/dynamic";
import Link from "next/link";
import { useRouter } from "next/router";
import { useRef, useState, useTransition, useEffect } from "react";
import { deleteResume } from "@/lib/resume-builder/resumes/actions";
import LoadingButton from "@/components/LoadingButton";

// Import the ResumePreview component with client-side only rendering
const DynamicResumePreview = dynamic(() => import("@/components/ResumePreview"), { 
  ssr: false,
  loading: () => <div className="aspect-[210/297] w-full bg-gray-100 animate-pulse"></div>
});

interface ResumeItemProps {
  resume: ResumeValues;
}

export default function ResumeItem({ resume = {} as ResumeValues }: ResumeItemProps) {
  const contentRef = useRef<HTMLDivElement>(null);
  const [isBrowser, setIsBrowser] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const { toast } = useToast();
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  // For SSR - return minimal markup
  if (typeof window === 'undefined') {
    return (
      <div className="group relative rounded-lg border border-gray-200 bg-white pt-0.5 pb-3 px-3 shadow-sm">
        <div className="space-y-1">
          <div className="flex flex-col">
            <p className="line-clamp-1 text-sm font-semibold mb-0">Resume</p>
          </div>
          <div className="aspect-[210/297] w-full bg-gray-100"></div>
        </div>
      </div>
    );
  }

  // Delete handler
  const handleDelete = () => {
    startTransition(() => {
      deleteResume(resume?.id || "")
        .then((response) => {
          if (response.success) {
            toast({
              title: "Resume deleted",
              description: "Your resume has been deleted successfully.",
            });
            setShowConfirmDialog(false);
            // Refresh the page to show updated data
            router.reload();
          }
        })
        .catch((error) => {
          console.error(error);
          toast({
            title: "Error",
            description: "Failed to delete resume. Please try again.",
            variant: "destructive",
          });
        });
    });
  };

  // Set isBrowser to true after component mounts
  useEffect(() => {
    setIsBrowser(true);
  }, []);

  return (
    <div className="group relative rounded-lg border border-gray-200 bg-white pt-0.5 pb-3 px-3 shadow-sm transition-all hover:shadow-md">
      <div className="space-y-1">
        <Link
          href={`/resume-builder/editor?resumeId=${resume?.id}`}
          className="inline-block w-full text-center"
        >
          <div className="flex flex-col">
            <p className="line-clamp-1 text-sm font-semibold mb-0">
              {resume?.title || "No title"}
            </p>
            {resume?.description && (
              <p className="line-clamp-1 text-xs text-gray-500 mt-0">
                {resume.description}
              </p>
            )}
          </div>
        </Link>
        <Link
          href={`/resume-builder/editor?resumeId=${resume?.id}`}
          className="relative inline-block w-full"
        >
          <DynamicResumePreview
            resumeData={resume}
            contentRef={contentRef}
            className="overflow-hidden shadow-sm transition-shadow group-hover:shadow-lg"
          />
          <div className="absolute inset-x-0 bottom-0 h-16 bg-gradient-to-t from-white to-transparent" />
        </Link>
      </div>
      
      {/* Action buttons */}
      {isBrowser && (
        <div className="absolute right-2 top-2 opacity-0 transition-opacity group-hover:opacity-100">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link href={`/resume-builder/editor?resumeId=${resume?.id}`}>
                  <Pencil className="mr-2 h-4 w-4" />
                  Edit
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => setShowConfirmDialog(true)}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )}

      {/* Delete confirmation dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete resume</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this resume? This action cannot be
              undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowConfirmDialog(false)}
              disabled={isPending}
            >
              Cancel
            </Button>
            <LoadingButton
              variant="destructive"
              onClick={handleDelete}
              loading={isPending}
            >
              Delete
            </LoadingButton>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 