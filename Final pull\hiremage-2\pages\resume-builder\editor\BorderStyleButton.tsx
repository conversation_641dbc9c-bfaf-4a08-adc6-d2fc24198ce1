import { Button } from "@/components/ui/button";
import { Circle, Square, Squircle } from "lucide-react";

export const BorderStyles = {
  SQUARE: "square",
  CIRCLE: "circle",
  SQUIRCLE: "squircle",
};

const borderStyles = Object.values(BorderStyles);

interface BorderStyleButtonProps {
  borderStyle: string | undefined;
  onChange: (borderStyle: string) => void;
}

export default function BorderStyleButton({
  borderStyle,
  onChange,
}: BorderStyleButtonProps) {
  function handleClick() {
    const currentIndex = borderStyle ? borderStyles.indexOf(borderStyle) : 0;
    const nextIndex = (currentIndex + 1) % borderStyles.length;
    onChange(borderStyles[nextIndex]);
  }

  const currentStyle = borderStyle || BorderStyles.SQUARE;

  return (
    <Button
      variant="outline"
      size="icon"
      title="Change border style"
      onClick={handleClick}
      className="relative bg-background/90 border-2"
    >
      {currentStyle === BorderStyles.SQUARE && (
        <Square className="size-5 fill-foreground/20 text-foreground" />
      )}
      {currentStyle === BorderStyles.CIRCLE && (
        <Circle className="size-5 fill-foreground/20 text-foreground" />
      )}
      {currentStyle === BorderStyles.SQUIRCLE && (
        <Squircle className="size-5 fill-foreground/20 text-foreground" />
      )}
    </Button>
  );
}
