import type { NextApiRequest, NextApiResponse } from 'next';
import { MongoClient } from 'mongodb';

interface Resume {
  resumeId: string;
  fileName: string;
  uploadDate: string;
  userId: string;
  s3Key: string;
  s3Bucket: string;
  metadata?: {
    fileName: string;
    uploadDate: string;
    userId: string;
    resumeId: string;
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const client = new MongoClient(process.env.MONGODB_URI ?? '');
  
  try {
    await client.connect();
    // Query from the same database where resumes are stored in storeResumeS3.ts
    const db = client.db('resume-metadata');
    const collection = db.collection('resumes');

    // Get the user ID from the request headers
    const userId = req.headers['x-user-id'] || 'default-user';

    // Try to fetch resumes using both the old and new document structures
    let resumes = await collection
      .find({ userId: userId })
      .toArray();

    // If no results, try the alternative structure
    if (!resumes.length) {
      resumes = await collection
        .find({ 'metadata.userId': userId })
        .toArray();
      
      // If still no results, also check the chatbot-service-prod database as fallback
      if (!resumes.length) {
        const chatbotDb = client.db('chatbot-service-prod');
        const candidateCollection = chatbotDb.collection('candidate_data');
        resumes = await candidateCollection
          .find({ 'metadata.userId': userId })
          .toArray();
      }
    }

    console.log(`Found ${resumes.length} resumes for user ${userId}`);

    // Transform the data to match our frontend interface, handling both document formats
    const transformedResumes = resumes.map(resume => {
      // Check if the resume has a nested metadata structure or flat structure
      const hasMetadata = resume.metadata && typeof resume.metadata === 'object';
      
      return {
        id: resume._id?.toString() || resume.resumeId,
        fileName: hasMetadata ? resume.metadata.fileName : resume.fileName,
        uploadDate: hasMetadata ? resume.metadata.uploadDate : resume.uploadDate,
        resumeId: hasMetadata ? resume.metadata.resumeId : resume.resumeId,
      };
    });

    return res.status(200).json(transformedResumes);
  } catch (err: any) {
    console.error('Error fetching resumes:', err);
    return res.status(500).json({ error: err.message });
  } finally {
    await client.close();
  }
} 