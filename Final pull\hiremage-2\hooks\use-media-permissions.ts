'use client';

import { useState, useCallback } from 'react';

interface MediaPermissions {
  audio: boolean;
  video: boolean;
  screen: boolean;
}

export function useMediaPermissions() {
  const [permissions, setPermissions] = useState<MediaPermissions>({
    audio: false,
    video: false,
    screen: false,
  });

  const requestPermission = useCallback(async (type: keyof MediaPermissions) => {
    try {
      if (type === 'screen') {
        await navigator.mediaDevices.getDisplayMedia({ video: true });
        setPermissions(prev => ({ ...prev, screen: true }));
        return true;
      } else {
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: type === 'audio',
          video: type === 'video',
        });
        stream.getTracks().forEach(track => track.stop());
        setPermissions(prev => ({ ...prev, [type]: true }));
        return true;
      }
    } catch (error) {
      console.error(`Failed to get ${type} permission:`, error);
      return false;
    }
  }, []);

  return { permissions, requestPermission };
}