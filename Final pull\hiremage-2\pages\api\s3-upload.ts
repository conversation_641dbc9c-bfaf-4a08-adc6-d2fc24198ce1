import { NextApiRequest, NextApiResponse } from 'next';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { IncomingForm } from 'formidable';
import fs from 'fs';

// Disable the default body parser
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Get S3 configuration
  const region = process.env.AWS_REGION || 'us-east-1';
  const accessKeyId = process.env.AWS_ACCESS_KEY_ID;
  const secretAccessKey = process.env.AWS_SECRET_ACCESS_KEY;
  const bucketName = process.env.AWS_S3_BUCKET_NAME || 'clueiva';

  if (!accessKeyId || !secretAccessKey || !bucketName) {
    return res.status(500).json({ error: 'Server configuration error' });
  }

  try {
    // Initialize S3 client
    const s3Client = new S3Client({
      region,
      credentials: {
        accessKeyId,
        secretAccessKey,
      },
    });

    // Parse form with debugging
    const form = new IncomingForm({
      keepExtensions: true,
      maxFileSize: 10 * 1024 * 1024 // 10MB
    });
    
    const [fields, files] = await new Promise<[any, any]>((resolve, reject) => {
      form.parse(req, (err, fields, files) => {
        if (err) {
          return reject(err);
        }
        resolve([fields, files]);
      });
    });

    // Get file object (handle both object and array formats)
    const fileObj = files.file;
    
    // Check if file exists in the expected format
    if (!fileObj) {
      return res.status(400).json({ error: 'No file provided' });
    }
    
    // Handle both array and single file formats
    const file = Array.isArray(fileObj) ? fileObj[0] : fileObj;
    
    // Get filepath - handle both v3 and v4 formidable APIs
    const filepath = file.filepath || file.path;
    
    if (!filepath) {
      return res.status(500).json({ error: 'File path not found in uploaded file' });
    }

    // Get file details - handle formidable v3/v4 differences
    const originalFilename = file.originalFilename || file.name || 'unnamed';
    const mimetype = file.mimetype || file.type || 'application/octet-stream';
    
    // Read file content
    try {
      const fileContent = fs.readFileSync(filepath);
      
      // Generate a unique file name
      const fileName = `resume-photos/${Date.now()}-${originalFilename}`;

      // Upload to S3
      await s3Client.send(new PutObjectCommand({
        Bucket: bucketName,
        Key: fileName,
        Body: fileContent,
        ContentType: mimetype,
        ACL: 'public-read',
      }));

      try {
        // Clean up temp file
        fs.unlinkSync(filepath);
      } catch (unlinkError) {
        // Continue anyway
      }

      // Generate URL
      const fileUrl = `https://${bucketName}.s3.${region}.amazonaws.com/${fileName}`;

      return res.status(200).json({ url: fileUrl });
    } catch (readError) {
      return res.status(500).json({ 
        error: 'Failed to read uploaded file',
        message: readError instanceof Error ? readError.message : String(readError)
      });
    }
  } catch (error) {
    return res.status(500).json({ 
      error: 'Upload failed', 
      message: error instanceof Error ? error.message : String(error)
    });
  }
} 