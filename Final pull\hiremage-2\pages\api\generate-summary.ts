import openai from "@/lib/openai";
import { GenerateSummaryInput, generateSummarySchema } from "@/lib/validation";
import type { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  // Check if OpenAI API key is configured
  if (!process.env.OPENAI_API_KEY) {
    return res.status(500).json({ 
      error: "OpenAI API key is not configured in environment variables. Please add your OpenAI API key to .env file." 
    });
  }

  try {
    const input = req.body as GenerateSummaryInput;
    const { jobTitle, workExperiences, educations, skills } =
      generateSummarySchema.parse(input);

    const systemMessage = `
      You are a job resume generator AI. Your task is to write a professional introduction summary for a resume given the user's provided data.
      Only return the summary and do not include any other information in the response. Keep it concise and professional.
      `;

    const userMessage = `
      Please generate a professional resume summary from this data:

      Job title: ${jobTitle || "N/A"}

      Work experience:
      ${workExperiences
        ?.map(
          (exp) => `
          Position: ${exp.position || "N/A"} at ${exp.company || "N/A"} from ${exp.startDate || "N/A"} to ${exp.endDate || "Present"}

          Description:
          ${exp.description || "N/A"}
          `,
        )
        .join("\n\n")}

        Education:
      ${educations
        ?.map(
          (edu) => `
          Degree: ${edu.degree || "N/A"} at ${edu.school || "N/A"} from ${edu.startDate || "N/A"} to ${edu.endDate || "N/A"}
          `,
        )
        .join("\n\n")}

        Skills:
        ${skills}
      `;

    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: systemMessage,
        },
        {
          role: "user",
          content: userMessage,
        },
      ],
    });

    const aiResponse = completion.choices[0].message.content;

    if (!aiResponse) {
      throw new Error("Failed to generate AI response");
    }

    return res.status(200).json({ summary: aiResponse });
  } catch (error: any) {
    console.error("Error generating summary:", error);
    return res.status(500).json({ error: error.message || "Something went wrong" });
  }
} 