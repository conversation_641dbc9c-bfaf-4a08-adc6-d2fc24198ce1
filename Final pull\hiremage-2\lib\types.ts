import { ResumeValues } from "./validation";

export interface EditorFormProps {
  resumeData: ResumeValues;
  setResumeData: (data: ResumeValues) => void;
}

export const resumeDataInclude = {
  workExperiences: true,
  educations: true,
};

export interface WorkExperience {
  id: string;
  position: string;
  company: string;
  startDate: string;
  endDate?: string;
  description?: string;
  current?: boolean;
}

export interface Education {
  id: string;
  institution: string;
  degree: string;
  fieldOfStudy?: string;
  startDate: string;
  endDate?: string;
  description?: string;
  current?: boolean;
  school?: string;
}

export interface ResumeServerData {
  id: string;
  userId: string;
  title: string;
  description?: string;
  photoUrl?: string;
  firstName?: string;
  lastName?: string;
  jobTitle?: string;
  city?: string;
  country?: string;
  phone?: string;
  email?: string;
  skills?: string[];
  borderStyle?: string;
  colorHex?: string;
  summary?: string;
  createdAt: Date;
  updatedAt: Date;
  workExperiences: WorkExperience[];
  educations: Education[];
}
