import React, { useEffect, useState } from "react";
import SectionLoader from "@/components/SectionLoader";
import { ACTIVE } from "@/helpers/coreConstant";
import { useSignin } from "@/hooks/authentication.hook";
import { IRootState } from "@/store";
import { GoogleLogin } from "@react-oauth/google";
import { useSelector } from "react-redux";
import BlankLayout from "@/components/Layouts/BlankLayout";
import GitHubLogin from "@/components/Common/GithubLogin.comp";
import Link from "next/link";
import RootLoader from "@/components/RootLoader";
import { useTranslation } from "react-i18next";

const LoginCover = () => {
  const { settings } = useSelector((state: IRootState) => state.common.data);
  const { t } = useTranslation();

  const {
    errors,
    handleSubmit,
    register,
    handleLogin,
    isLoading,
    handleGoogleLogin,
    setValue, // Function to set form field values
  } = useSignin();

  const demoUsers = [
    { name: "User", email: "<EMAIL>", password: "123456" },
    { name: "Admin", email: "<EMAIL>", password: "123456" },
    // Add more demo users as needed
  ];

  const selectDemoUser = (email: string, password: string) => {
    setValue("email", email);
    setValue("password", password);
  };
  useEffect(() => {
    if (process.env.NEXT_PUBLIC_SHOW_LOGIN_CREDENTIAL === "1") {
      setValue("email", "<EMAIL>");
      setValue("password", "123456");
    }
  }, []);
  if (isLoading) return <RootLoader />;

  return (
    <div className="flex min-h-screen dark:text-white ">
      <div className="relative hidden min-h-screen w-1/2 flex-col items-center justify-center p-4 text-white before:absolute before:-z-10 before:h-full before:w-full before:bg-black before:bg-[url('/assets/images/ai-intro.gif')] before:bg-cover before:bg-left before:bg-no-repeat before:bg-blend-darken lg:flex">
        <div>
          <Link href={`/`}>
            <img
              src={settings?.site_logo ? settings?.site_logo : " "}
              alt=""
              className="mb-2 inline-block h-[90px] max-w-full"
            />
          </Link>
        </div>
        <h3 className="mb-4 text-center text-3xl font-bold">
          {t("Join the AI Revolution")}
        </h3>
        <p className="mb-7">
          {t(
            "Experience the power of AI with our SaaS platform. Join us and unlock new levels of productivity and creativity."
          )}
        </p>
      </div>
      <div className="relative flex w-full items-center justify-center bg-white dark:bg-black lg:w-1/2">
        <div className="w-full max-w-md rounded-lg p-5 md:p-10">
          <h2 className="mb-3 text-center text-3xl font-bold">{t("Login")}</h2>
          <form
            className="space-y-5"
            onSubmit={handleSubmit((data) => {
              handleLogin(data.email, data.password);
            })}
          >
            <div>
              <label
                htmlFor="email"
                className="block text-gray-700 dark:text-white"
              >
                {t("Email")}
              </label>
              <input
                id="email"
                type="email"
                className="form-input mt-2 rounded-md border-gray-300 focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 dark:border-dark"
                placeholder="Enter Email"
                {...register("email", { required: true })}
              />
              {errors.email?.type === "required" && (
                <p role="alert" className="mt-2 text-red-500">
                  {t("Email is required")}
                </p>
              )}
            </div>
            <div>
              <label
                htmlFor="password"
                className="block text-gray-700 dark:text-white"
              >
                {t("Password")}
              </label>
              <input
                id="password"
                type="password"
                className="form-input mt-2 rounded-md border-gray-300 focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 dark:border-dark"
                placeholder="Enter Password"
                {...register("password", { required: true })}
              />
              {errors.password?.type === "required" && (
                <p role="alert" className="mt-2 text-red-500">
                  {t("Password is required")}
                </p>
              )}
            </div>

            <button
              type="submit"
              className="btn btn-primary hover:bg-primary-dark mt-4 w-full rounded-md bg-primary py-2 px-4 text-white transition duration-300"
            >
              {isLoading && (
                <span className="inline-block h-5 w-5 animate-spin rounded-full border-2 border-white border-l-transparent align-middle ltr:mr-4 rtl:ml-4 dark:border-dark"></span>
              )}
              {t("Login")}
            </button>
            <div className="relative my-7 text-center md:mb-9">
              <span className="absolute inset-x-0 top-1/2 h-px w-full -translate-y-1/2 bg-white-light dark:bg-white-dark"></span>
              <span className="relative bg-white px-2 font-bold uppercase text-white-dark dark:bg-black dark:text-white-light">
                or
              </span>
            </div>
            <div className="flex flex-col items-center justify-center gap-y-4">
              {/* Continue with Google Button */}
              {Number(settings?.social_login_google_status) === ACTIVE && (
                <button
                  type="button"
                  onClick={() => {
                    window.location.href = `${process.env.NEXT_PUBLIC_BASE_URL}/auth/google`;
                  }}
                  className="social-login-button flex w-full items-center justify-center gap-3 rounded-lg border border-gray-300 bg-white px-4 py-3 text-gray-700 shadow-sm transition-all duration-200 hover:bg-gray-50 focus:outline-none focus:ring-0 active:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:hover:bg-gray-700"
                >
                  <svg
                    className="h-5 w-5"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                      fill="#4285F4"
                    />
                    <path
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                      fill="#34A853"
                    />
                    <path
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                      fill="#FBBC05"
                    />
                    <path
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                      fill="#EA4335"
                    />
                  </svg>
                  <span className="text-sm font-medium">
                    {t("Continue with Google")}
                  </span>
                </button>
              )}

              {/* GitHub Login */}
              {Number(settings?.social_login_github_status) === ACTIVE && (
                <GitHubLogin />
              )}

              {/* Legacy Google Login (keeping for fallback) */}
              {Number(settings?.social_login_google_status) === ACTIVE && (
                <div className="hidden">
                  <GoogleLogin
                    onSuccess={(credentialResponse: any) => {
                      handleGoogleLogin(
                        credentialResponse?.credential,
                        credentialResponse?.clientId
                      );
                    }}
                    type="icon"
                    shape="circle"
                    size="large"
                    theme="filled_black"
                    onError={() => {
                      console.log("Login Failed");
                    }}
                    useOneTap
                  />
                </div>
              )}
            </div>
          </form>

          {process.env.NEXT_PUBLIC_SHOW_LOGIN_CREDENTIAL === "1" && (
            <div className="mt-4 flex justify-center">
              <table className="table-auto border-none">
                <tbody>
                  {demoUsers.map((user, index) => (
                    <tr
                      key={index}
                      className="cursor-pointer border dark:border-dark"
                      onClick={() => selectDemoUser(user.email, user.password)}
                    >
                      <td className="px-4 py-2">{user.email}</td>
                      <td className="px-4 py-2">{user.password}</td>
                      <td className="px-4 py-2">
                        <button
                          className="btn border text-black shadow-none dark:border-dark dark:text-white"
                          onClick={() => {
                            selectDemoUser(user.email, user.password);
                            handleLogin(user.email, user.password);
                          }}
                        >
                          Use
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
          <div className="mt-8 flex items-center justify-center gap-x-2 text-center">
            <p>{t(`Don't have an account ?`)}</p>
            <Link href={`/register`} className="font-bold underline">
              {t("Sign Up")}
            </Link>
          </div>
          <div className="mt-8 flex items-center justify-center gap-x-2 text-center">
            <p>{t(`Forget Password?`)}</p>
            <Link href={`/forget-password`} className="font-bold underline">
              {t("Forget-password")}
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

LoginCover.getLayout = (page: any) => {
  return <BlankLayout>{page}</BlankLayout>;
};

export default LoginCover;
