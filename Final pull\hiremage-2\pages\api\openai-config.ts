import type { NextApiRequest, NextApiResponse } from 'next';

type Data = {
  hasKey: boolean;
  message?: string;
}

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<Data>
) {
  // Don't expose the actual key, just whether it exists
  const hasKey = !!process.env.OPENAI_API_KEY;
  
  res.status(200).json({
    hasKey,
    message: hasKey ? 
      "OpenAI API key is configured" : 
      "OpenAI API key is not configured. Please add your OPENAI_API_KEY to the .env file."
  });
} 